# Sansar Code Examples by Category

This document provides working code examples organized by functionality, based on real implementations from the repository.

## Table of Contents

1. [Interaction and UI](#interaction-and-ui)
2. [Animation Control](#animation-control)
3. [Physics and Movement](#physics-and-movement)
4. [Audio and Media](#audio-and-media)
5. [User Management](#user-management)
6. [Data Storage](#data-storage)
7. [External Integration](#external-integration)
8. [Quest and Game Logic](#quest-and-game-logic)
9. [Utility and Tools](#utility-and-tools)
10. [Advanced Techniques](#advanced-techniques)

## Interaction and UI

### Basic Click Interaction
```csharp
// From Examples/MyScripts/HelloWorld.cs
public class HelloWorld : SceneObjectScript
{
    [DefaultValue("Click Me!")]
    public Interaction MyInteraction;

    public override void Init()
    {
        MyInteraction.Subscribe(OnClick);
    }

    public void OnClick(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        agent.SendChat("Hello! Thanks for clicking me.");
    }
}
```

### Complex Interaction with Permissions
```csharp
// Based on Users/GranddadGotMojo/TriggerComplexInteraction.cs
public class ComplexInteraction : SceneObjectScript
{
    [DefaultValue("Complex Action")]
    public Interaction ComplexInteraction;
    
    [DefaultValue(false)]
    public bool OwnerOnly = false;
    
    [DefaultValue(0.0)]
    public double CooldownSeconds = 0.0;
    
    private DateTime lastUsed = DateTime.MinValue;

    public override void Init()
    {
        ComplexInteraction.Subscribe(OnInteract);
    }

    private void OnInteract(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        // Check permissions
        if (OwnerOnly && !IsSceneOwner(agent))
        {
            agent.SendChat("Only the scene owner can use this.");
            return;
        }

        // Check cooldown
        if (CooldownSeconds > 0)
        {
            double elapsed = (DateTime.Now - lastUsed).TotalSeconds;
            if (elapsed < CooldownSeconds)
            {
                agent.SendChat($"Please wait {(CooldownSeconds - elapsed):F1} more seconds.");
                return;
            }
        }

        // Perform action
        PerformComplexAction(agent);
        lastUsed = DateTime.Now;
    }

    private void PerformComplexAction(AgentPrivate agent)
    {
        agent.SendChat("Complex action performed!");
        // Add your complex logic here
    }

    private bool IsSceneOwner(AgentPrivate agent)
    {
        return agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
    }
}
```

### Modal Dialog Display
```csharp
// From Users/GranddadGotMojo/DisplayModalMessageFromTriggerVolume.cs
public class ModalDialog : SceneObjectScript
{
    [DefaultValue("Show Dialog")]
    public Interaction ShowDialogInteraction;
    
    [DefaultValue("Dialog Title")]
    public string DialogTitle = "Dialog Title";
    
    [DefaultValue("This is the dialog content.")]
    public string DialogContent = "This is the dialog content.";
    
    [DefaultValue("OK")]
    public string ButtonText = "OK";

    public override void Init()
    {
        ShowDialogInteraction.Subscribe(OnShowDialog);
    }

    private void OnShowDialog(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        agent.Client.UI.ModalDialog.Show(DialogTitle, DialogContent, ButtonText);
    }
}
```

## Animation Control

### Basic Animation Playback
```csharp
// From Examples/AnimationExample.cs
public class AnimationExample : SceneObjectScript
{
    [DefaultValue("Play Animation")]
    public Interaction PlayInteraction;
    
    [DefaultValue("MyAnimation")]
    public string AnimationName = "MyAnimation";

    private AnimationComponent animationComponent;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            PlayInteraction.Subscribe(OnPlay);
        }
    }

    private void OnPlay(InteractionData data)
    {
        var animation = animationComponent.GetAnimation(AnimationName);
        if (animation != null)
        {
            animation.Play();
        }
    }
}
```

### Advanced Animation with Events
```csharp
// Enhanced from Users/GranddadGotMojo/LetterAnimation2.cs pattern
public class AnimationSequence : SceneObjectScript
{
    [DefaultValue("Start Sequence")]
    public Interaction StartInteraction;
    
    private AnimationComponent animationComponent;
    private string[] animationSequence = { "Anim1", "Anim2", "Anim3" };
    private int currentAnimationIndex = 0;
    private bool isPlaying = false;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            StartInteraction.Subscribe(OnStart);
            
            // Subscribe to all animation events
            foreach (string animName in animationSequence)
            {
                animationComponent.Subscribe(animName, OnAnimationEvent);
            }
        }
    }

    private void OnStart(InteractionData data)
    {
        if (!isPlaying)
        {
            currentAnimationIndex = 0;
            isPlaying = true;
            PlayNextAnimation();
        }
    }

    private void OnAnimationEvent(AnimationData data)
    {
        if (data.Phase == AnimationEventPhase.Done && isPlaying)
        {
            currentAnimationIndex++;
            if (currentAnimationIndex < animationSequence.Length)
            {
                Wait(0.5); // Brief pause between animations
                PlayNextAnimation();
            }
            else
            {
                isPlaying = false;
                Log.Write("Animation sequence complete");
            }
        }
    }

    private void PlayNextAnimation()
    {
        if (currentAnimationIndex < animationSequence.Length)
        {
            var animation = animationComponent.GetAnimation(animationSequence[currentAnimationIndex]);
            if (animation != null)
            {
                animation.Play();
            }
        }
    }
}
```

## Physics and Movement

### Simple Object Mover
```csharp
// From Examples/MoverExample1.cs
public class SimpleMover : SceneObjectScript
{
    [DefaultValue("Move Object")]
    public Interaction MoveInteraction;
    
    [DefaultValue("<0,0,5>")]
    public Vector TargetPosition = new Vector(0, 0, 5);
    
    [DefaultValue(2.0)]
    public double MoveDuration = 2.0;

    private AnimationComponent animationComponent;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            MoveInteraction.Subscribe(OnMove);
        }
    }

    private void OnMove(InteractionData data)
    {
        StartCoroutine(MoveToPosition);
    }

    private void MoveToPosition()
    {
        var startPos = animationComponent.GetPosition();
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddSeconds(MoveDuration);

        while (DateTime.UtcNow < endTime)
        {
            double progress = (DateTime.UtcNow - startTime).TotalSeconds / MoveDuration;
            progress = Math.Min(1.0, Math.Max(0.0, progress));
            
            Vector currentPos = Vector.Lerp(startPos, TargetPosition, (float)progress);
            animationComponent.SetPosition(currentPos);
            
            Wait(0.05); // 20 FPS update rate
        }
        
        animationComponent.SetPosition(TargetPosition);
    }
}
```

### Advanced Patrolling Mover
```csharp
// From Samples/PatrolMoverScript.cs
public class PatrolMover : SceneObjectScript
{
    [DefaultValue("Start Patrol")]
    public Interaction StartInteraction;
    
    private Vector[] patrolPoints = {
        new Vector(0, 0, 0),
        new Vector(5, 0, 0),
        new Vector(5, 0, 5),
        new Vector(0, 0, 5)
    };
    
    [DefaultValue(3.0)]
    public double MoveDuration = 3.0;
    
    [DefaultValue(1.0)]
    public double PauseAtPoints = 1.0;
    
    private AnimationComponent animationComponent;
    private bool isPatrolling = false;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            StartInteraction.Subscribe(OnStartPatrol);
        }
    }

    private void OnStartPatrol(InteractionData data)
    {
        if (!isPatrolling)
        {
            isPatrolling = true;
            StartCoroutine(PatrolLoop);
        }
    }

    private void PatrolLoop()
    {
        int currentPoint = 0;
        
        while (isPatrolling)
        {
            Vector targetPos = patrolPoints[currentPoint];
            yield return MoveToPosition(targetPos);
            
            Wait(PauseAtPoints);
            
            currentPoint = (currentPoint + 1) % patrolPoints.Length;
        }
    }

    private void MoveToPosition(Vector targetPos)
    {
        Vector startPos = animationComponent.GetPosition();
        DateTime startTime = DateTime.UtcNow;
        DateTime endTime = startTime.AddSeconds(MoveDuration);

        while (DateTime.UtcNow < endTime && isPatrolling)
        {
            double progress = (DateTime.UtcNow - startTime).TotalSeconds / MoveDuration;
            progress = Math.Min(1.0, Math.Max(0.0, progress));
            
            Vector currentPos = Vector.Lerp(startPos, targetPos, (float)progress);
            animationComponent.SetPosition(currentPos);
            
            Wait(0.05);
        }
        
        if (isPatrolling)
        {
            animationComponent.SetPosition(targetPos);
        }
    }
}
```

### Gravity Controller
```csharp
// From Examples/GravityExample.cs
public class GravityController : SceneObjectScript
{
    [DefaultValue("Toggle Gravity")]
    public Interaction GravityInteraction;
    
    [Range(-50.0, 50.0)]
    [DefaultValue(-9.8)]
    public double GravityStrength = -9.8;
    
    [DefaultValue(false)]
    public bool LowGravityMode = false;

    public override void Init()
    {
        GravityInteraction.Subscribe(OnToggleGravity);
    }

    private void OnToggleGravity(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        LowGravityMode = !LowGravityMode;
        
        if (LowGravityMode)
        {
            agent.SetGravity(new Vector(0, (float)(GravityStrength * 0.1), 0));
            agent.SendChat("Low gravity enabled!");
        }
        else
        {
            agent.SetGravity(new Vector(0, (float)GravityStrength, 0));
            agent.SendChat("Normal gravity restored!");
        }
    }
}
```

## Audio and Media

### Positional Audio Controller
```csharp
// From Users/binah/audio/AudioController.cs patterns
public class AudioController : SceneObjectScript
{
    [DefaultValue("Play Audio")]
    public Interaction PlayInteraction;
    
    public SoundResource[] AudioClips;
    
    [Range(0.0, 60.0)]
    [DefaultValue(10.0)]
    public double Volume = 10.0;
    
    [DefaultValue(false)]
    public bool RandomizeClips = false;
    
    [DefaultValue(false)]
    public bool LoopAudio = false;

    private Random random = new Random();
    private PlayHandle currentPlayHandle;

    public override void Init()
    {
        if (AudioClips != null && AudioClips.Length > 0)
        {
            PlayInteraction.Subscribe(OnPlay);
        }
    }

    private void OnPlay(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        // Stop current audio if playing
        if (currentPlayHandle != null)
        {
            currentPlayHandle.Stop();
        }

        // Select audio clip
        SoundResource clipToPlay;
        if (RandomizeClips && AudioClips.Length > 1)
        {
            clipToPlay = AudioClips[random.Next(AudioClips.Length)];
        }
        else
        {
            clipToPlay = AudioClips[0];
        }

        // Configure playback settings
        PlaySettings settings = LoopAudio ? PlaySettings.Looped : PlaySettings.PlayOnce;
        settings.Loudness = (float)Volume;

        // Play audio
        currentPlayHandle = ScenePrivate.PlaySound(clipToPlay, settings);
        
        if (currentPlayHandle != null)
        {
            currentPlayHandle.OnFinished(() => {
                currentPlayHandle = null;
            });
        }
    }
}
```

### Media Screen Controller
```csharp
// From Users/GranddadGotMojo/gotMojoMediaScreen2.cs
public class MediaScreenController : SceneObjectScript
{
    [DefaultValue("Change Media")]
    public Interaction ChangeMediaInteraction;
    
    public MediaResource[] MediaFiles;
    
    [DefaultValue(false)]
    public bool CycleMedia = true;
    
    [DefaultValue(0)]
    public int StartingMediaIndex = 0;

    private int currentMediaIndex = 0;
    private MediaComponent mediaComponent;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out mediaComponent) && 
            MediaFiles != null && MediaFiles.Length > 0)
        {
            currentMediaIndex = StartingMediaIndex;
            ChangeMediaInteraction.Subscribe(OnChangeMedia);
            
            // Set initial media
            SetMedia(currentMediaIndex);
        }
    }

    private void OnChangeMedia(InteractionData data)
    {
        if (CycleMedia)
        {
            currentMediaIndex = (currentMediaIndex + 1) % MediaFiles.Length;
        }
        else
        {
            // Allow user to select specific media
            AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
            if (agent != null)
            {
                agent.SendChat($"Current media: {currentMediaIndex + 1}/{MediaFiles.Length}");
            }
        }
        
        SetMedia(currentMediaIndex);
    }

    private void SetMedia(int index)
    {
        if (index >= 0 && index < MediaFiles.Length)
        {
            mediaComponent.SetMedia(MediaFiles[index], MediaSettings.Default);
        }
    }
}
```

## User Management

### User Tracking and Announcements
```csharp
// From Examples/CoroutineExample.cs enhanced
public class UserTracker : SceneObjectScript
{
    [DefaultValue(true)]
    public bool AnnounceJoinLeave = true;
    
    [DefaultValue(true)]
    public bool TrackVisitTime = true;
    
    [DefaultValue("user_joined")]
    public string UserJoinedEvent = "user_joined";
    
    [DefaultValue("user_left")]
    public string UserLeftEvent = "user_left";

    private Dictionary<SessionId, DateTime> userJoinTimes = new Dictionary<SessionId, DateTime>();

    public override void Init()
    {
        StartCoroutine(TrackUsers);
    }

    private void TrackUsers()
    {
        while (true)
        {
            UserData userData = (UserData)WaitFor(ScenePrivate.User.Subscribe, User.AddUser, SessionId.Invalid);
            
            AgentPrivate agent = ScenePrivate.FindAgent(userData.User);
            if (agent != null && agent.IsValid)
            {
                if (AnnounceJoinLeave)
                {
                    ScenePrivate.Chat.MessageAllUsers($"{agent.AgentInfo.Name} has joined the scene.");
                }
                
                if (TrackVisitTime)
                {
                    userJoinTimes[userData.User] = DateTime.UtcNow;
                    StartCoroutine(TrackUserVisit, userData.User);
                }
                
                // Send join event
                PostScriptEvent(UserJoinedEvent, new SimpleScriptEventData(agent.AgentInfo.Name));
            }
        }
    }

    private void TrackUserVisit(SessionId userId)
    {
        WaitFor(ScenePrivate.User.Subscribe, User.RemoveUser, userId);
        
        DateTime joinTime;
        if (userJoinTimes.TryGetValue(userId, out joinTime))
        {
            TimeSpan visitDuration = DateTime.UtcNow - joinTime;
            
            if (AnnounceJoinLeave)
            {
                ScenePrivate.Chat.MessageAllUsers(
                    $"A visitor was present for {visitDuration.TotalMinutes:F1} minutes.");
            }
            
            userJoinTimes.Remove(userId);
            
            // Send leave event
            PostScriptEvent(UserLeftEvent, new SimpleScriptEventData($"Visit duration: {visitDuration.TotalSeconds:F1}s"));
        }
    }
}
```

### Permission System
```csharp
// Based on Users/binah/access-control/access-control.cs
public class AccessControl : SceneObjectScript
{
    [DefaultValue("Restricted Action")]
    public Interaction RestrictedInteraction;
    
    [DefaultValue("")]
    public string AllowedUsers = ""; // Comma-separated list
    
    [DefaultValue(true)]
    public bool OwnerAlwaysAllowed = true;
    
    [DefaultValue("Access denied.")]
    public string DeniedMessage = "Access denied.";

    private HashSet<string> allowedUserSet = new HashSet<string>();

    public override void Init()
    {
        ProcessAllowedUsers();
        RestrictedInteraction.Subscribe(OnRestrictedAction);
    }

    private void ProcessAllowedUsers()
    {
        if (!string.IsNullOrWhiteSpace(AllowedUsers))
        {
            string[] users = AllowedUsers.Split(',');
            foreach (string user in users)
            {
                string trimmedUser = user.Trim().ToLower();
                if (!string.IsNullOrEmpty(trimmedUser))
                {
                    allowedUserSet.Add(trimmedUser);
                }
            }
        }
    }

    private void OnRestrictedAction(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (IsUserAllowed(agent))
        {
            PerformRestrictedAction(agent);
        }
        else
        {
            agent.SendChat(DeniedMessage);
        }
    }

    private bool IsUserAllowed(AgentPrivate agent)
    {
        if (OwnerAlwaysAllowed && IsSceneOwner(agent))
        {
            return true;
        }
        
        string userHandle = agent.AgentInfo.Handle.ToLower();
        return allowedUserSet.Contains(userHandle);
    }

    private bool IsSceneOwner(AgentPrivate agent)
    {
        return agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
    }

    private void PerformRestrictedAction(AgentPrivate agent)
    {
        agent.SendChat("Access granted! Action performed.");
        // Add your restricted functionality here
    }
}
```

## Data Storage

### Persistent Counter
```csharp
// From Examples/DataStoreExample.cs enhanced
public class PersistentCounter : SceneObjectScript
{
    [DefaultValue("Increment Counter")]
    public Interaction IncrementInteraction;
    
    [DefaultValue("Show Count")]
    public Interaction ShowCountInteraction;
    
    [DefaultValue("Reset Counter")]
    public Interaction ResetInteraction;
    
    [DefaultValue("scene_counter")]
    public string CounterKey = "scene_counter";

    public override void Init()
    {
        IncrementInteraction.Subscribe(OnIncrement);
        ShowCountInteraction.Subscribe(OnShowCount);
        ResetInteraction.Subscribe(OnReset);
    }

    private void OnIncrement(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        StartCoroutine(IncrementCounter, agent);
    }

    private void IncrementCounter(AgentPrivate agent)
    {
        // Get current count
        var getResult = WaitFor(DataStore.GetValue, CounterKey) as DataStore.Result<string>;
        
        int currentCount = 0;
        if (getResult.Success && int.TryParse(getResult.Value, out currentCount))
        {
            // Value exists, increment it
        }
        
        currentCount++;
        
        // Save new count
        var setResult = WaitFor(DataStore.SetValue, CounterKey, currentCount.ToString()) as DataStore.Result;
        
        if (setResult.Success)
        {
            agent.SendChat($"Counter incremented to {currentCount}");
        }
        else
        {
            agent.SendChat("Failed to save counter value");
            Log.Write(LogLevel.Error, "Counter", setResult.Message);
        }
    }

    private void OnShowCount(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        StartCoroutine(ShowCounter, agent);
    }

    private void ShowCounter(AgentPrivate agent)
    {
        var result = WaitFor(DataStore.GetValue, CounterKey) as DataStore.Result<string>;
        
        if (result.Success)
        {
            if (int.TryParse(result.Value, out int count))
            {
                agent.SendChat($"Current count: {count}");
            }
            else
            {
                agent.SendChat("Counter data is corrupted");
            }
        }
        else
        {
            agent.SendChat("No counter data found (count is 0)");
        }
    }

    private void OnReset(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (!IsSceneOwner(agent))
        {
            agent.SendChat("Only the scene owner can reset the counter");
            return;
        }

        StartCoroutine(ResetCounter, agent);
    }

    private void ResetCounter(AgentPrivate agent)
    {
        var result = WaitFor(DataStore.SetValue, CounterKey, "0") as DataStore.Result;
        
        if (result.Success)
        {
            agent.SendChat("Counter reset to 0");
        }
        else
        {
            agent.SendChat("Failed to reset counter");
        }
    }

    private bool IsSceneOwner(AgentPrivate agent)
    {
        return agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
    }
}
```

## External Integration

### HTTP API Client
```csharp
// From Examples/HttpClientExample.cs enhanced
public class APIClient : SceneObjectScript
{
    [DefaultValue("Fetch Weather")]
    public Interaction FetchInteraction;
    
    [DefaultValue("https://api.openweathermap.org/data/2.5/weather")]
    public string WeatherApiUrl = "https://api.openweathermap.org/data/2.5/weather";
    
    [DefaultValue("your_api_key_here")]
    public string ApiKey = "your_api_key_here";
    
    [DefaultValue("New York")]
    public string DefaultCity = "New York";
    
    [DefaultValue(10)]
    public int TimeoutSeconds = 10;

    public override void Init()
    {
        FetchInteraction.Subscribe(OnFetch);
    }

    private void OnFetch(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (ApiKey == "your_api_key_here")
        {
            agent.SendChat("Please configure your API key first!");
            return;
        }

        agent.SendChat("Fetching weather data...");
        StartCoroutine(FetchWeatherData, agent);
    }

    private void FetchWeatherData(AgentPrivate agent)
    {
        try
        {
            string url = $"{WeatherApiUrl}?q={DefaultCity}&appid={ApiKey}&units=metric";
            
            HttpClient client = ScenePrivate.CreateHttpClient();
            HttpRequestOptions options = new HttpRequestOptions();
            options.Method = HttpRequestMethod.GET;
            options.Timeout = TimeSpan.FromSeconds(TimeoutSeconds);

            HttpRequestData response = WaitFor(client.Request, url, options) as HttpRequestData;

            if (response.Success)
            {
                ProcessWeatherResponse(agent, response.Body);
            }
            else
            {
                agent.SendChat($"Weather request failed: {response.Message}");
                Log.Write(LogLevel.Warning, "Weather", $"Request failed: {response.Message}");
            }
        }
        catch (Exception e)
        {
            agent.SendChat("Error occurred while fetching weather data.");
            Log.Write(LogLevel.Error, "Weather", $"Exception: {e.Message}");
        }
    }

    private void ProcessWeatherResponse(AgentPrivate agent, string jsonResponse)
    {
        try
        {
            // Simple JSON parsing for temperature (in a real implementation, use a proper JSON parser)
            if (jsonResponse.Contains("\"temp\":"))
            {
                int tempStart = jsonResponse.IndexOf("\"temp\":") + 7;
                int tempEnd = jsonResponse.IndexOf(",", tempStart);
                if (tempEnd == -1) tempEnd = jsonResponse.IndexOf("}", tempStart);
                
                string tempStr = jsonResponse.Substring(tempStart, tempEnd - tempStart);
                if (double.TryParse(tempStr, out double temperature))
                {
                    agent.SendChat($"Current temperature in {DefaultCity}: {temperature:F1}°C");
                    return;
                }
            }
            
            agent.SendChat("Weather data received but couldn't parse temperature.");
        }
        catch (Exception e)
        {
            Log.Write(LogLevel.Warning, "Weather", $"Parsing error: {e.Message}");
            agent.SendChat("Error parsing weather data.");
        }
    }
}
```

### Twitch Integration
```csharp
// From Examples/TwitchEventExample.cs
public class TwitchIntegration : SceneObjectScript
{
    [DefaultValue("Connect to Twitch")]
    public Interaction ConnectInteraction;
    
    [DefaultValue("your_channel_name")]
    public string TwitchChannel = "your_channel_name";

    public override void Init()
    {
        ConnectInteraction.Subscribe(OnConnect);
    }

    private void OnConnect(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (TwitchChannel == "your_channel_name")
        {
            agent.SendChat("Please configure your Twitch channel name first!");
            return;
        }

        StartCoroutine(ConnectToTwitch, agent);
    }

    private void ConnectToTwitch(AgentPrivate agent)
    {
        try
        {
            TwitchClient twitchClient = ScenePrivate.CreateTwitchClient();
            
            TwitchConnectOptions options = new TwitchConnectOptions();
            options.Channel = TwitchChannel;
            
            TwitchConnectData connectResult = WaitFor(twitchClient.Connect, options) as TwitchConnectData;
            
            if (connectResult.Success)
            {
                agent.SendChat($"Connected to Twitch channel: {TwitchChannel}");
                
                // Subscribe to chat messages
                twitchClient.Subscribe(TwitchEventType.ChatMessage, OnTwitchMessage);
                
                // Subscribe to follow events
                twitchClient.Subscribe(TwitchEventType.Follow, OnTwitchFollow);
            }
            else
            {
                agent.SendChat($"Failed to connect to Twitch: {connectResult.Message}");
            }
        }
        catch (Exception e)
        {
            agent.SendChat("Error connecting to Twitch.");
            Log.Write(LogLevel.Error, "Twitch", $"Connection error: {e.Message}");
        }
    }

    private void OnTwitchMessage(TwitchEventData data)
    {
        if (data.EventType == TwitchEventType.ChatMessage)
        {
            TwitchChatMessageData chatData = data as TwitchChatMessageData;
            ScenePrivate.Chat.MessageAllUsers($"[Twitch] {chatData.UserName}: {chatData.Message}");
        }
    }

    private void OnTwitchFollow(TwitchEventData data)
    {
        if (data.EventType == TwitchEventType.Follow)
        {
            TwitchFollowData followData = data as TwitchFollowData;
            ScenePrivate.Chat.MessageAllUsers($"New Twitch follower: {followData.UserName}!");
            
            // Trigger celebration effect
            PostScriptEvent("celebration", new SimpleScriptEventData(followData.UserName));
        }
    }
}
```

## Quest and Game Logic

### Simple Quest Giver
```csharp
// From Examples/ScriptLibrary/Quest/QuestGiver.cs pattern
public class QuestGiver : SceneObjectScript
{
    [DefaultValue("Talk to Quest Giver")]
    public Interaction TalkInteraction;
    
    [DefaultValue("Collect 5 items")]
    public string QuestDescription = "Collect 5 items";
    
    [DefaultValue("quest_started")]
    public string QuestStartEvent = "quest_started";
    
    [DefaultValue("quest_completed")]
    public string QuestCompletedEvent = "quest_completed";

    private HashSet<SessionId> playersWithQuest = new HashSet<SessionId>();

    public override void Init()
    {
        TalkInteraction.Subscribe(OnTalk);
        SubscribeToScriptEvent("item_collected", OnItemCollected);
    }

    private void OnTalk(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (playersWithQuest.Contains(data.AgentId))
        {
            agent.SendChat("You already have this quest! Check your progress.");
        }
        else
        {
            GiveQuest(agent);
        }
    }

    private void GiveQuest(AgentPrivate agent)
    {
        playersWithQuest.Add(agent.AgentInfo.SessionId);
        
        agent.SendChat($"Quest accepted: {QuestDescription}");
        
        // Send quest start event with player data
        var questData = new SimpleScriptEventData($"Player:{agent.AgentInfo.Name}");
        PostScriptEvent(QuestStartEvent, questData);
    }

    private void OnItemCollected(ScriptEventData data)
    {
        // Parse the event data to check quest progress
        string eventData = data.Data?.AsInterface<ISimpleData>()?.ExtraData?.ToString() ?? "";
        
        if (eventData.Contains("Player:") && eventData.Contains("Items:"))
        {
            // Extract player name and item count
            string playerName = ExtractPlayerName(eventData);
            int itemCount = ExtractItemCount(eventData);
            
            if (itemCount >= 5)
            {
                CompleteQuest(playerName);
            }
        }
    }

    private void CompleteQuest(string playerName)
    {
        // Find the player and complete their quest
        foreach (var agent in ScenePrivate.GetAgents())
        {
            if (agent.AgentInfo.Name == playerName && playersWithQuest.Contains(agent.AgentInfo.SessionId))
            {
                playersWithQuest.Remove(agent.AgentInfo.SessionId);
                agent.SendChat("Quest completed! Well done!");
                
                PostScriptEvent(QuestCompletedEvent, new SimpleScriptEventData(playerName));
                break;
            }
        }
    }

    private string ExtractPlayerName(string data)
    {
        int start = data.IndexOf("Player:") + 7;
        int end = data.IndexOf(" ", start);
        if (end == -1) end = data.Length;
        return data.Substring(start, end - start);
    }

    private int ExtractItemCount(string data)
    {
        int start = data.IndexOf("Items:") + 6;
        int end = data.IndexOf(" ", start);
        if (end == -1) end = data.Length;
        
        if (int.TryParse(data.Substring(start, end - start), out int count))
        {
            return count;
        }
        return 0;
    }
}
```

### Game State Manager
```csharp
// Based on Users/GranddadGotMojo/LogicSequenceChecker.cs
public class GameStateManager : SceneObjectScript
{
    public enum GameState
    {
        Waiting,
        Playing,
        Finished
    }

    [DefaultValue("Start Game")]
    public Interaction StartInteraction;
    
    [DefaultValue("Reset Game")]
    public Interaction ResetInteraction;
    
    [DefaultValue(60.0)]
    public double GameDurationSeconds = 60.0;

    private GameState currentState = GameState.Waiting;
    private DateTime gameStartTime;
    private int playerScore = 0;

    public override void Init()
    {
        StartInteraction.Subscribe(OnStartGame);
        ResetInteraction.Subscribe(OnResetGame);
        
        SubscribeToScriptEvent("score_point", OnScorePoint);
        
        UpdateInteractionPrompts();
    }

    private void OnStartGame(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (currentState == GameState.Waiting)
        {
            StartGame(agent);
        }
        else if (currentState == GameState.Playing)
        {
            double timeLeft = GameDurationSeconds - (DateTime.UtcNow - gameStartTime).TotalSeconds;
            agent.SendChat($"Game in progress! Time left: {timeLeft:F1} seconds");
        }
        else
        {
            agent.SendChat($"Game finished! Final score: {playerScore}. Reset to play again.");
        }
    }

    private void OnResetGame(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        ResetGame();
        agent.SendChat("Game reset!");
    }

    private void StartGame(AgentPrivate agent)
    {
        currentState = GameState.Playing;
        gameStartTime = DateTime.UtcNow;
        playerScore = 0;
        
        agent.SendChat($"Game started! You have {GameDurationSeconds} seconds to score points!");
        ScenePrivate.Chat.MessageAllUsers("A new game has started!");
        
        UpdateInteractionPrompts();
        
        // Send game start event
        PostScriptEvent("game_started", new SimpleScriptEventData($"Duration:{GameDurationSeconds}"));
        
        StartCoroutine(GameTimer);
    }

    private void GameTimer()
    {
        Wait(GameDurationSeconds);
        
        if (currentState == GameState.Playing)
        {
            EndGame();
        }
    }

    private void EndGame()
    {
        currentState = GameState.Finished;
        
        ScenePrivate.Chat.MessageAllUsers($"Game finished! Final score: {playerScore}");
        
        UpdateInteractionPrompts();
        
        // Send game end event
        PostScriptEvent("game_ended", new SimpleScriptEventData($"Score:{playerScore}"));
    }

    private void ResetGame()
    {
        currentState = GameState.Waiting;
        playerScore = 0;
        
        UpdateInteractionPrompts();
        
        PostScriptEvent("game_reset", new SimpleScriptEventData(""));
    }

    private void OnScorePoint(ScriptEventData data)
    {
        if (currentState == GameState.Playing)
        {
            playerScore++;
            ScenePrivate.Chat.MessageAllUsers($"Point scored! Total: {playerScore}");
        }
    }

    private void UpdateInteractionPrompts()
    {
        switch (currentState)
        {
            case GameState.Waiting:
                StartInteraction.SetPrompt("Start Game");
                break;
            case GameState.Playing:
                StartInteraction.SetPrompt($"Playing... (Score: {playerScore})");
                break;
            case GameState.Finished:
                StartInteraction.SetPrompt($"Finished (Score: {playerScore})");
                break;
        }
    }
}
```

## Utility and Tools

### Scene Reset Controller
```csharp
// From Users/binah/reset/reset-scene.cs
public class SceneResetController : SceneObjectScript
{
    [DefaultValue("reset-scene")]
    public string ResetEvent = "reset-scene";
    
    [DefaultValue(true)]
    public bool EventEnabled = true;
    
    [DefaultValue("/reset-scene")]
    public string ResetChatCommand = "/reset-scene";
    
    [DefaultValue(true)]
    public bool ChatEnabled = true;
    
    [DefaultValue("Reset Scene!")]
    public Interaction ResetInteraction;
    
    [DefaultValue(true)]
    public bool InteractionEnabled = true;
    
    [DefaultValue(true)]
    public bool DebugLogging = true;

    private string _resetChatCommand;

    public override void Init()
    {
        if (EventEnabled)
        {
            SubscribeToScriptEvent(ResetEvent, OnResetEvent);
            if (DebugLogging) Log.Write($"Subscribed to reset event: {ResetEvent}");
        }

        if (ChatEnabled)
        {
            _resetChatCommand = "/" + ResetChatCommand.TrimStart('/');
            ScenePrivate.Chat.Subscribe(0, null, OnChat);
            if (DebugLogging) Log.Write($"Subscribed to chat command: {_resetChatCommand}");
        }

        if (InteractionEnabled)
        {
            ResetInteraction.Subscribe(OnResetInteraction);
            if (DebugLogging) Log.Write("Subscribed to reset interaction");
        }
        else
        {
            ResetInteraction.SetPrompt("");
        }
    }

    private void OnResetEvent(ScriptEventData data)
    {
        if (DebugLogging) Log.Write("Reset triggered by event");
        PerformReset();
    }

    private void OnResetInteraction(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        if (!IsSceneOwner(agent))
        {
            agent.SendChat("Only the scene owner can reset the scene");
            return;
        }

        if (DebugLogging) Log.Write($"Reset triggered by interaction from {agent.AgentInfo.Name}");
        PerformReset();
    }

    private void OnChat(ChatData data)
    {
        var words = data.Message.Split(' ');
        
        if (words[0].ToLower() == _resetChatCommand.ToLower())
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
            if (agent == null || !agent.IsValid) return;

            if (!IsSceneOwner(agent))
            {
                agent.SendChat("Only the scene owner can reset the scene");
                return;
            }

            if (DebugLogging) Log.Write($"Reset triggered by chat command from {agent.AgentInfo.Name}");
            PerformReset();
        }
    }

    private void PerformReset()
    {
        if (DebugLogging) Log.Write("Performing scene reset");
        
        ScenePrivate.Chat.MessageAllUsers("Scene is being reset...");
        
        Wait(1.0); // Brief delay for the message
        
        ScenePrivate.ResetScene();
    }

    private bool IsSceneOwner(AgentPrivate agent)
    {
        return agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
    }
}
```

### Debug and Logging Helper
```csharp
// Based on Examples/ScriptLibrary/LibraryDebugger.cs patterns
public class DebugHelper : SceneObjectScript
{
    [DefaultValue("Debug Info")]
    public Interaction DebugInteraction;
    
    [DefaultValue(true)]
    public bool VerboseLogging = true;
    
    [DefaultValue(true)]
    public bool ShowMemoryInfo = true;
    
    [DefaultValue(true)]
    public bool ShowCoroutineInfo = true;

    public override void Init()
    {
        DebugInteraction.Subscribe(OnDebugInfo);
        
        if (VerboseLogging)
        {
            Log.Write(LogLevel.Info, "Debug", "Debug helper initialized");
        }
    }

    private void OnDebugInfo(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        string debugInfo = GatherDebugInfo();
        
        // Send debug info to requesting user
        agent.SendChat(debugInfo);
        
        if (VerboseLogging)
        {
            Log.Write(LogLevel.Info, "Debug", $"Debug info requested by {agent.AgentInfo.Name}");
        }
    }

    private string GatherDebugInfo()
    {
        var info = new System.Text.StringBuilder();
        
        info.AppendLine("=== Debug Information ===");
        
        // Script info
        info.AppendLine($"Script ID: {Script.ID}");
        info.AppendLine($"Object ID: {ObjectPrivate.ObjectId}");
        info.AppendLine($"Object Name: {ObjectPrivate.Name}");
        
        // Memory info
        if (ShowMemoryInfo)
        {
            info.AppendLine($"Memory Usage: {Memory.Usage:F2} MB");
            info.AppendLine($"Memory Limit: {Memory.Limit:F2} MB");
            info.AppendLine($"Pending Events: {PendingEventCount}");
        }
        
        // Coroutine info
        if (ShowCoroutineInfo)
        {
            info.AppendLine($"Active Coroutines: {GetCoroutineCount()}");
            info.AppendLine($"Max Coroutines: {MaxCoroutines}");
        }
        
        // Scene info
        info.AppendLine($"Scene Users: {ScenePrivate.GetAgents().Count()}");
        
        return info.ToString();
    }

    // Utility method for other scripts to log with consistent formatting
    public static void LogInfo(string component, string message)
    {
        Log.Write(LogLevel.Info, $"[{component}]", message);
    }

    public static void LogWarning(string component, string message)
    {
        Log.Write(LogLevel.Warning, $"[{component}]", message);
    }

    public static void LogError(string component, string message)
    {
        Log.Write(LogLevel.Error, $"[{component}]", message);
    }
}
```

## Advanced Techniques

### Promise-Based Async Operations
```csharp
// From Users/evoav/Promises/Promises.cs patterns
using EvoAv.Promises;

public class PromiseExample : SceneObjectScript
{
    [DefaultValue("Start Async Operation")]
    public Interaction StartInteraction;

    public override void Init()
    {
        StartInteraction.Subscribe(OnStart);
    }

    private void OnStart(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        agent.SendChat("Starting async operation...");
        
        PerformAsyncOperation()
            .Then(result => {
                agent.SendChat($"Operation completed: {result}");
            })
            .Catch(error => {
                agent.SendChat($"Operation failed: {error.Message}");
                Log.Write(LogLevel.Error, "Promise", error.Message);
            })
            .Done();
    }

    private IPromise<string> PerformAsyncOperation()
    {
        return new Promise<string>((resolve, reject) => {
            StartCoroutine(() => {
                try
                {
                    // Simulate async work
                    Wait(2.0);
                    
                    // Simulate random success/failure
                    if (new Random().Next(2) == 0)
                    {
                        resolve("Success! Data processed.");
                    }
                    else
                    {
                        reject(new Exception("Random failure occurred"));
                    }
                }
                catch (Exception e)
                {
                    reject(e);
                }
            });
        });
    }

    private IPromise<string> ChainedOperations()
    {
        return FetchData()
            .Then(data => ProcessData(data))
            .Then(processedData => SaveData(processedData))
            .Then(saveResult => $"Pipeline complete: {saveResult}");
    }

    private IPromise<string> FetchData()
    {
        return new Promise<string>((resolve, reject) => {
            StartCoroutine(() => {
                Wait(1.0);
                resolve("Raw data from source");
            });
        });
    }

    private IPromise<string> ProcessData(string rawData)
    {
        return new Promise<string>((resolve, reject) => {
            StartCoroutine(() => {
                Wait(0.5);
                resolve($"Processed: {rawData}");
            });
        });
    }

    private IPromise<string> SaveData(string processedData)
    {
        return new Promise<string>((resolve, reject) => {
            StartCoroutine(() => {
                Wait(0.5);
                resolve($"Saved: {processedData}");
            });
        });
    }
}
```

### Advanced State Machine
```csharp
// Enhanced state machine with transition validation
public class AdvancedStateMachine : SceneObjectScript
{
    public enum State
    {
        Idle,
        Preparing,
        Active,
        Cooling,
        Error
    }

    public enum Trigger
    {
        Start,
        Ready,
        Stop,
        Reset,
        Fault
    }

    [DefaultValue("Send Trigger")]
    public Interaction TriggerInteraction;

    private State currentState = State.Idle;
    private Dictionary<(State, Trigger), State> transitions;
    private Dictionary<State, Action> stateActions;
    private Queue<Trigger> pendingTriggers = new Queue<Trigger>();

    public override void Init()
    {
        SetupStateMachine();
        TriggerInteraction.Subscribe(OnTrigger);
        
        StartCoroutine(ProcessTriggers);
    }

    private void SetupStateMachine()
    {
        // Define valid state transitions
        transitions = new Dictionary<(State, Trigger), State>
        {
            { (State.Idle, Trigger.Start), State.Preparing },
            { (State.Preparing, Trigger.Ready), State.Active },
            { (State.Active, Trigger.Stop), State.Cooling },
            { (State.Cooling, Trigger.Reset), State.Idle },
            { (State.Error, Trigger.Reset), State.Idle }
        };

        // Add fault transitions from any state
        foreach (State state in Enum.GetValues(typeof(State)))
        {
            if (state != State.Error)
            {
                transitions[(state, Trigger.Fault)] = State.Error;
            }
        }

        // Define state entry actions
        stateActions = new Dictionary<State, Action>
        {
            { State.Idle, OnIdleEnter },
            { State.Preparing, OnPreparingEnter },
            { State.Active, OnActiveEnter },
            { State.Cooling, OnCoolingEnter },
            { State.Error, OnErrorEnter }
        };
    }

    private void OnTrigger(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        // Simple trigger selection for demo
        Trigger trigger = (Trigger)(new Random().Next(Enum.GetValues(typeof(Trigger)).Length));
        
        agent.SendChat($"Sending trigger: {trigger}");
        pendingTriggers.Enqueue(trigger);
    }

    private void ProcessTriggers()
    {
        while (true)
        {
            if (pendingTriggers.Count > 0)
            {
                Trigger trigger = pendingTriggers.Dequeue();
                ProcessTrigger(trigger);
            }
            
            Wait(0.1);
        }
    }

    private void ProcessTrigger(Trigger trigger)
    {
        var transitionKey = (currentState, trigger);
        
        if (transitions.ContainsKey(transitionKey))
        {
            State newState = transitions[transitionKey];
            
            Log.Write($"State transition: {currentState} -> {newState} (trigger: {trigger})");
            
            currentState = newState;
            
            // Execute state entry action
            if (stateActions.ContainsKey(newState))
            {
                stateActions[newState]?.Invoke();
            }
            
            UpdateDisplay();
        }
        else
        {
            Log.Write(LogLevel.Warning, "StateMachine", 
                $"Invalid transition: {currentState} + {trigger}");
        }
    }

    private void OnIdleEnter()
    {
        ScenePrivate.Chat.MessageAllUsers("System is idle and ready.");
    }

    private void OnPreparingEnter()
    {
        ScenePrivate.Chat.MessageAllUsers("System is preparing...");
        StartCoroutine(AutoReadyTimer);
    }

    private void OnActiveEnter()
    {
        ScenePrivate.Chat.MessageAllUsers("System is now active!");
    }

    private void OnCoolingEnter()
    {
        ScenePrivate.Chat.MessageAllUsers("System is cooling down...");
        StartCoroutine(AutoCooldownTimer);
    }

    private void OnErrorEnter()
    {
        ScenePrivate.Chat.MessageAllUsers("System error detected!");
    }

    private void AutoReadyTimer()
    {
        Wait(3.0);
        if (currentState == State.Preparing)
        {
            pendingTriggers.Enqueue(Trigger.Ready);
        }
    }

    private void AutoCooldownTimer()
    {
        Wait(5.0);
        if (currentState == State.Cooling)
        {
            pendingTriggers.Enqueue(Trigger.Reset);
        }
    }

    private void UpdateDisplay()
    {
        TriggerInteraction.SetPrompt($"State: {currentState}");
    }
}
```

This collection of examples demonstrates the breadth and depth of Sansar scripting capabilities, providing practical, working code that can be adapted for specific use cases. Each example includes error handling, user feedback, and follows established best practices for Sansar script development.