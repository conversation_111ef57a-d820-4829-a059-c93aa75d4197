## Trigger Multiple Chat Commands

This a simpler approach to tie a slash "/" chat commands to an event than the existing simple chat command.  You have a single line to configure where you type in a series of event names that you want to send based on a slash "/" chat commands of the same name.  For example, typing /dj1spin in chat would send the dj1spin event.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/TriggerMultipleChatCommands.png)
