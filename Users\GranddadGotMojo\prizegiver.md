# Prize Giver

Give Store Items as Prizes to Sansar Avatars.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/prizegiverscript.png)

**Prize Cmd** - the keyword in chat that denotes that you want to run the Prize Giver Script.

**Prizes** - an array of Product Id GUIDS of items on the store that have been set to hidden.

To use this script, the prize giver types in chat a command in the following format:
* slash(/)
* The Prize Cmd name
* space
* The avatar name to receive the prize
* comma
* The Prize number in the Prizes List 

In the example below it would be **/prize gotMojo,1**

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/prizegiver2.png)

If you were the GotMojo avatar in the experience when the prize commands was entered in the chat command by the prize giver you would see the following dialog to claim your prize of buy an item.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/
