# Tetris Block Configuration Guide for Grid_blocks_new Script

This guide explains how to configure the Grid_blocks_new script with Tetris pieces (tetrominoes) for building a Tetris-style game in Sansar.

## Overview

The Grid_blocks_new script uses a configuration-time approach where all Tetris pieces must be set up before the game starts. You'll need to create 3D models of each Tetris piece and configure them through the script's properties panel.

## Required Tetris Pieces (Tetrominoes)

You'll need to create 7 basic Tetris pieces, each in 4 rotational states (28 total pieces):

### 1. I-Piece (Line)
- **Shape**: Four blocks in a straight line
- **Rotations**: Horizontal (4×1) and Vertical (1×4)
- **Color**: Cyan/Light Blue

### 2. O-Piece (Square)
- **Shape**: 2×2 square
- **Rotations**: Only 1 rotation needed (square is symmetrical)
- **Color**: Yellow

### 3. T-Piece
- **Shape**: T-shaped piece
- **Rotations**: 4 orientations (up, right, down, left)
- **Color**: Purple/Magenta

### 4. S-Piece (Skew)
- **Shape**: S-shaped zigzag
- **Rotations**: 2 orientations (horizontal and vertical)
- **Color**: Green

### 5. Z-Piece (Reverse Skew)
- **Shape**: Z-shaped zigzag
- **Rotations**: 2 orientations (horizontal and vertical)
- **Color**: Red

### 6. J-Piece
- **Shape**: L-shaped, mirrored
- **Rotations**: 4 orientations
- **Color**: Blue

### 7. L-Piece
- **Shape**: L-shaped
- **Rotations**: 4 orientations
- **Color**: Orange

## Step-by-Step Configuration Process

### Step 1: Create 3D Models
1. **Design each Tetris piece** in your preferred 3D modeling software
2. **Use consistent scale**: Each block should be 1×1×1 units
3. **Apply appropriate colors** to match traditional Tetris colors
4. **Export as FBX or OBJ** files compatible with Sansar

### Step 2: Import to Sansar
1. **Upload your models** to Sansar through the Asset Pipeline
2. **Create ClusterResource objects** for each piece
3. **Save to your inventory** for easy access during configuration

### Step 3: Configure the Script

#### Access Script Properties
1. **Attach the Grid_blocks_new script** to a terrain plane in your scene
2. **Select the terrain object** with the script
3. **Open the Properties panel** in the Sansar editor
4. **Locate the script properties** section

#### Configure Block Lists
The script provides 8 lists (BlockObjects through BlockObjects8) with 20 slots each:

**Recommended Organization:**
- **BlockObjects (1-20)**: All I-piece rotations, O-piece, T-piece rotations
- **BlockObjects2 (21-40)**: S-piece, Z-piece, J-piece, L-piece rotations
- **BlockObjects3-8**: Reserved for additional pieces or variations

#### Adding Tetris Pieces
1. **Click "Add entry"** in the BlockObjects list
2. **Select your Tetris piece ClusterResource** from inventory
3. **Repeat for each piece** following this suggested order:

```
BlockObjects List (Slots 1-20):
1. I-Piece Horizontal
2. I-Piece Vertical
3. O-Piece
4. T-Piece Up
5. T-Piece Right
6. T-Piece Down
7. T-Piece Left
8. S-Piece Horizontal
9. S-Piece Vertical
10. Z-Piece Horizontal
11. Z-Piece Vertical
12. J-Piece Up
13. J-Piece Right
14. J-Piece Down
15. J-Piece Left
16. L-Piece Up
17. L-Piece Right
18. L-Piece Down
19. L-Piece Left
20. (Reserved)
```

### Step 4: Configure Game Settings

#### Grid Settings
- **Grid Size**: Set to `1.0` for standard Tetris block size
- **Max Height**: Set to `20.0` for a standard Tetris playfield height
- **Enable Dynamic Grid Sizes**: `false` (keep consistent grid)

#### Sound Settings
- **Place Block Sound**: Add a satisfying "click" or "thud" sound
- **Delete Block Sound**: Add a "pop" or "break" sound for line clearing
- **Sound Volume**: Set to `70-80` for good feedback

#### Timing Settings
- **Block Placement Delay**: Set to `0.1` to prevent spam clicking
- **Emissive Pulse Duration**: Set to `0.5` for visual feedback

## Runtime Controls for Tetris

Once configured, players can use these controls:

### Basic Controls
- **Left Click**: Place the current Tetris piece
- **Key "2"**: Cycle through different Tetris pieces
- **Key "3"**: Toggle DELETE mode (for clearing lines)
- **Key "4"**: Toggle ROTATE mode (rotate pieces in place)
- **Key "5"**: Toggle COPY mode (copy piece types from placed blocks)

### Game-Specific Usage
1. **Building Phase**: Use BUILD mode to place Tetris pieces
2. **Line Clearing**: Switch to DELETE mode to remove completed lines
3. **Piece Selection**: Use Key "2" to cycle through available pieces
4. **Rotation**: Use ROTATE mode to orient pieces before placement

## Tips for Tetris Game Design

### Visual Design
- **Use bright, contrasting colors** for each piece type
- **Add slight emissive properties** to make pieces glow
- **Consider transparency effects** for preview pieces

### Gameplay Considerations
- **Create a spawn area** at the top of the playfield
- **Design clear boundaries** for the play area
- **Consider adding a "next piece" preview system**

### Performance Optimization
- **Keep piece models low-poly** for better performance
- **Use efficient textures** and materials
- **Limit the number of simultaneous pieces**

## Troubleshooting

### Common Issues
- **"No block objects configured" error**: Ensure at least one piece is added to BlockObjects
- **Pieces not appearing**: Check that ClusterResource objects are properly saved
- **Wrong piece sizes**: Verify all pieces use consistent 1×1×1 block scale
- **Missing rotations**: Ensure all 4 rotations are created for asymmetrical pieces

### Validation
- **Test each piece**: Cycle through all pieces using Key "2"
- **Check rotations**: Use ROTATE mode to verify piece orientations
- **Verify colors**: Ensure pieces are easily distinguishable

## Advanced Features

### Custom Piece Variations
- **Add special pieces** in BlockObjects3-8 for power-ups
- **Create themed variations** (different materials, effects)
- **Design larger pieces** for advanced gameplay modes

### Integration with Other Systems
- **Combine with scoring scripts** for competitive play
- **Add timer systems** for time-based challenges
- **Integrate with multiplayer systems** for competitive Tetris

This configuration method provides a solid foundation for creating a Tetris-style building game using the Grid_blocks_new script's robust block management system.
