/* Minecraft-like Grid Building Script for Sansar
 * Attach this script to a terrain plane to enable grid-based block building
 *
 * Controls:
 * - Left click: Place/Delete/Rotate/Copy/Swap blocks (depending on current mode)
 * - Key "1": Toggle between BUILD and SWAP modes
 * - Key "2": Cycle through different block types
 * - Key "3": Toggle between BUILD and DELETE modes
 * - Key "4": Toggle between BUILD and ROTATE modes
 * - Key "5": Toggle between BUILD and COPY modes
 * - Key "6": Toggle between BUILD and TINT modes
 * - Key "7": Toggle between BUILD and PASTE_TINT modes
 
 * - Key "9": Toggle between Grid Sizes modes
 * - Key "0": Toggle between disable gridsnap or free placement
 *
 * Rotation Mode:
 * - Click on top/bottom faces: Rotate horizontally (around Y-axis)
 * - Click on side faces: Rotate vertically (around X or Z-axis)
 * - Only works with keyframed blocks (set Motion Type to "Keyframed" before saving to inventory)
 *
 * Copy Mode:
 * - Click on any placed block to copy its type
 * - Automatically switches to BUILD mode after copying
 * - Allows you to quickly pick block types from the world instead of cycling through the list
 *
 * Swap Mode:
 * - Click on any placed block to cycle through all available block types
 * - Visually swaps the mesh while preserving position and rotation
 * - Allows live preview of different block types without rebuilding
 */

using Sansar;
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;
using System.Linq;

public class Grid_blocks : SceneObjectScript
{
    // Defines for grid object data
    private const int MAX_GRID_SIZE = 100; // Max grid length in meters
    private const float BLOCK_SNAP_DISTANCE = .2f; // Distance block snaps to grid
    
    // Interaction prompt text (used during block selection)
    private const string prompt = "Interact";
    
    // New constant for TINT_MODE
    private const int TINT_MODE = 5;
    
    // Add a new mode constant for Paste_Tint
    private const int PASTE_TINT_MODE = 6;
    
    #region ScriptParameters
    [Tooltip("List of block types - use 'Add entry' to add ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects")]
    [MinEntries(1)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects;

    [Tooltip("Additional block types (21-40) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 2")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects2;

    [Tooltip("Additional block types (41-60) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 3")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects3;

    [Tooltip("Additional block types (61-80) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 4")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects4;

    [Tooltip("Additional block types (81-100) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 5")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects5;

    [Tooltip("Additional block types (101-120) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 6")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects6;

    [Tooltip("Additional block types (121-140) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 7")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects7;

    [Tooltip("Additional block types (141-160) - use 'Add entry' to add more ClusterResource objects from inventory or scene")]
    [DisplayName("Block Objects 8")]
    [MinEntries(0)]
    [MaxEntries(20)]
    public readonly List<ClusterResource> BlockObjects8;

    [Tooltip("Size of each grid cell in world units")]
    [DefaultValue(1.0f)]
    [DisplayName("Grid Size")]
    public readonly float GridSize;

    [Tooltip("Enable dynamic grid sizing with key 9 to cycle through different grid sizes")]
    [DefaultValue(false)]
    [DisplayName("Enable Dynamic Grid Sizes")]
    public readonly bool EnableDynamicGridSizes;

    [Tooltip("Comma-separated list of grid sizes to cycle through (e.g., 0.5,1.0,2.0,4.0,8.0). Only used when Dynamic Grid Sizes is enabled.")]
    [DefaultValue("0.5,1.0,2.0,4.0,8.0")]
    [DisplayName("Dynamic Grid Sizes")]
    public readonly string DynamicGridSizesList;

    [Tooltip("Maximum height for block placement")]
    [DefaultValue(50.0f)]
    [DisplayName("Max Height")]
    public readonly float MaxHeight;

    [Tooltip("Enable debug logging")]
    [DefaultValue(false)]
    [DisplayName("Debug Mode")]
    public readonly bool DebugMode;

    [Tooltip("Sound to play when placing a block. Optional - leave empty for silent operation. Select any audio resource from your inventory.")]
    [DisplayName("Place Block Sound")]
    public SoundResource PlaceBlockSound;

    [Tooltip("Sound to play when deleting a block. Optional - leave empty for silent operation. Select any audio resource from your inventory.")]
    [DisplayName("Delete Block Sound")]
    public SoundResource DeleteBlockSound;

    [Tooltip("Volume for block placement and deletion sounds (0-100)")]
    [DefaultValue(50.0f)]
    [Range(0, 100)]
    [DisplayName("Sound Volume")]
    public readonly float SoundVolume;

    [Tooltip("Duration in seconds for the emissive pulse effect when placing blocks with emissive materials (0 = disabled, 0.1 minimum)")]
    [DefaultValue(1.5f)]
    [Range(0.0f, 10.0f)]
    [DisplayName("Emissive Pulse Duration")]
    public readonly float EmissivePulseDuration;

    [Tooltip("Minimum delay in seconds between block placements per user (0 = no delay, 0.1 = 100ms delay). Helps prevent spam clicking and ensures proper block tracking.")]
    [DefaultValue(0.1f)]
    [Range(0.0f, 2.0f)]
    [DisplayName("Block Placement Delay")]
    public readonly float BlockPlacementDelay;

    [Tooltip("Cluster for Reaction 1 (Swap Mode)")]
    public ThumbnailedClusterResource ReactionCluster1;
    [Tooltip("Reaction 1 Name")]
    public string ReactionName1 = "Swap";
    [Tooltip("Action for Reaction 1")]
    public string ReactionAction1 = "Action1";

    [Tooltip("Cluster for Reaction 2 (Block Type Mode)")]
    public ThumbnailedClusterResource ReactionCluster2;
    [Tooltip("Reaction 2 Name")]
    public string ReactionName2 = "Cycle";
    [Tooltip("Action for Reaction 2")]
    public string ReactionAction2 = "Action2";

    [Tooltip("Cluster for Reaction 3")]
    public ThumbnailedClusterResource ReactionCluster3;
    [Tooltip("Reaction 3 Name")]
    public string ReactionName3 = "Add";
    [Tooltip("Action for Reaction 3")]
    public string ReactionAction3 = "Action3";

    [Tooltip("Cluster for Reaction 4")]
    public ThumbnailedClusterResource ReactionCluster4;
    [Tooltip("Reaction 4 Name")]
    public string ReactionName4 = "Remove";
    [Tooltip("Action for Reaction 4")]
    public string ReactionAction4 = "Action4";

    [Tooltip("Cluster for Reaction 5")]
    public ThumbnailedClusterResource ReactionCluster5;
    [Tooltip("Reaction 5 Name")]
    public string ReactionName5 = "Tint";
    [Tooltip("Action for Reaction 5")]
    public string ReactionAction5 = "Action5";

    [Tooltip("Cluster for Reaction 6")]
    public ThumbnailedClusterResource ReactionCluster6;
    [Tooltip("Reaction 6 Name")]
    public string ReactionName6 = "Copy";
    [Tooltip("Action for Reaction 6")]
    public string ReactionAction6 = "Action6";

    [Tooltip("Cluster for Reaction 7")]
    public ThumbnailedClusterResource ReactionCluster7;
    [Tooltip("Reaction 7 Name")]
    public string ReactionName7 = "Mode7";
    [Tooltip("Action for Reaction 7")]
    public string ReactionAction7 = "Action7";

    [Tooltip("Cluster for Reaction 8")]
    public ThumbnailedClusterResource ReactionCluster8;
    [Tooltip("Reaction 8 Name")]
    public string ReactionName8 = "Mode8";
    [Tooltip("Action for Reaction 8")]
    public string ReactionAction8 = "Action8";

    [Tooltip("Cluster for Reaction 9")]
    public ThumbnailedClusterResource ReactionCluster9;
    [Tooltip("Reaction 9 Name")]
    public string ReactionName9 = "PasteTint";
    [Tooltip("Action for Reaction 9")]
    public string ReactionAction9 = "Paste_Tint";

    [Tooltip("Global offset for all reactions")]
    public Vector ReactionOffset = new Vector(0, 0, 1);
    [Tooltip("Global duration for all reactions")]
    public float ReactionDuration = 2.0f;

    // No exposed property for ground volume name; use hardcoded name
    private Vector groundCenter;
    private Vector groundExtents;
    private RigidBodyComponent groundVolume;

    [DefaultValue(false)]
    [DisplayName("Allow World Clean Command")]
    public readonly bool AllowWorldCleanCommand;
    #endregion

    // Dictionary to track placed blocks by grid position
    private Dictionary<string, Cluster> placedBlocks = new Dictionary<string, Cluster>();
    
    // Dictionary to track current block type per user
    private Dictionary<SessionId, int> userBlockTypes = new Dictionary<SessionId, int>();
    
    // Dictionary to track user modes: 0 = BUILD, 1 = DELETE, 2 = ROTATE, 3 = COPY, 4 = SWAP, 5 = TINT, 6 = PASTE_TINT
    private Dictionary<SessionId, int> userModes = new Dictionary<SessionId, int>();
    
    // Dictionary to track rotation state of each placed block
    private Dictionary<string, Quaternion> blockRotations = new Dictionary<string, Quaternion>();
    
    // Dictionary to track which block type each placed block is (for copying)
    private Dictionary<string, int> blockTypes = new Dictionary<string, int>();
    
    // Audio component for playing sounds (kept for potential future use)
    private AudioComponent audioComponent;
    
    // Track whether world cleaning is currently allowed
    private bool worldCleaningEnabled = true;
    
    // Dictionary to track grid state per user (true = grid on, false = grid off)
    private Dictionary<SessionId, bool> userGridEnabled = new Dictionary<SessionId, bool>();
    
    // Dictionary to track current grid size index per user (for dynamic grid sizes)
    private Dictionary<SessionId, int> userGridSizeIndex = new Dictionary<SessionId, int>();
    
    // Array of available grid sizes (parsed from DynamicGridSizesList)
    private float[] availableGridSizes;
    
    // Current effective grid size per user
    private Dictionary<SessionId, float> userCurrentGridSize = new Dictionary<SessionId, float>();
    
    // Track held movement keys per user in select mode
    private Dictionary<SessionId, HashSet<string>> userHeldMoveKeys = new Dictionary<SessionId, HashSet<string>>();
    private Dictionary<SessionId, bool> userMoveCoroutineActive = new Dictionary<SessionId, bool>();

    // Track last block placement time per user (for placement delay)
    private Dictionary<SessionId, DateTime> userLastPlacementTime = new Dictionary<SessionId, DateTime>();

    // Track selected block per user
    private Dictionary<SessionId, string> userSelectedBlock = new Dictionary<SessionId, string>();
    private Dictionary<SessionId, int> userPreviousMode = new Dictionary<SessionId, int>();

    // 1. Add at the top with other dictionaries:
    private Dictionary<SessionId, List<Color>> userCopiedTints = new Dictionary<SessionId, List<Color>>();

    // 2. Add a reference to Reactions
    private Reactions reactions;
    private IEventSubscription reactionSub;

    // Track last action per user for reaction toggling
    private Dictionary<SessionId, string> userLastAction = new Dictionary<SessionId, string>();

    // Add a dictionary to store the last copied tint per user
    private Dictionary<SessionId, Color> userLastCopiedTint = new Dictionary<SessionId, Color>();

    // Add a new dictionary to track copied rotations per user
    private Dictionary<SessionId, Quaternion> userCopiedRotations = new Dictionary<SessionId, Quaternion>();

    // Add this at the top with other dictionaries:
    private Dictionary<string, float> blockGridSizes = new Dictionary<string, float>();

    public override void Init()
    {
        // Fix #3: ObjectPrivate.BoundingBox does not exist. Use RigidBodyComponent.GetColliderExtents instead.
        // Original: var bounds = ObjectPrivate.BoundingBox;
        Vector meshSize = Vector.One; // Default size
        if (ObjectPrivate.TryGetFirstComponent(out RigidBodyComponent rigidBody))
        {
            Vector center = rigidBody.GetColliderCenter();
            Vector extents = rigidBody.GetColliderExtents();
            meshSize = extents * 2.0f; // Full size
        }

        // Fix #4–6: meshSize not defined. Replace with bounds.Extents directly (centered on bounds)
        // Already fixed above with proper extents calculation
        float x = meshSize.X;
        float y = meshSize.Y;
        float z = meshSize.Z;

        // Fix #7, #11: Debug missing. Replace Debug with Log
        // Original: Debug.Log("Message");
        Log.Write(LogLevel.Info, "[GridBlocks] Log message here");

        // Fix #8–9: ColliderComponent missing. Add correct using and check via ObjectPrivate.TryGetFirstComponent
        // Note: ColliderComponent doesn't exist in Sansar API, use RigidBodyComponent instead
        RigidBodyComponent collider;
        if (ObjectPrivate.TryGetFirstComponent(out collider))
        {
            // safe to use collider (RigidBodyComponent)
        }

        // Fix #13: AddInteractionData.Agent does not exist. Replace with InteractionData.AgentId
        // Note: AddInteractionData doesn't have AgentId, use InteractionData instead
        // Removed ObjectPrivate.AddInteraction to prevent terrain interaction prompt

        // Fix #15–18: MaterialProperties.HasProperty and AlbedoColor/BaseColor not valid.
        // Replace with GetProperties and check Tint/Emissive/Tiling/etc.
        uint meshCount = ObjectPrivate.GetComponentCount(ComponentType.MeshComponent);
        if (meshCount > 0)
        {
            var meshComp = ObjectPrivate.GetComponent(ComponentType.MeshComponent, 0) as MeshComponent;
            if (meshComp != null)
            {
                foreach (RenderMaterial mat in meshComp.GetRenderMaterials())
                {
                    var props = mat.GetProperties();
                    if (mat.HasTint)
                    {
                        Log.Write(LogLevel.Info, "Material has tint: " + props.Tint.ToString());
                    }
                }
            }
        }
        
        // Count non-null meshes in both BlockObjects lists
        int validMeshCount = GetValidMeshCount();

        // Validate block meshes
        if (validMeshCount == 0)
        {
            Log.Write(LogLevel.Error, GetType().Name, "No block objects configured! Please use 'Add entry' to add ClusterResource objects in the script properties.");
            return;
        }

        // Find and initialize the audio component
        uint audioCount = ObjectPrivate.GetComponentCount(ComponentType.AudioComponent);
        if (audioCount > 0)
        {
            audioComponent = (AudioComponent)ObjectPrivate.GetComponent(ComponentType.AudioComponent, 0);
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "AudioComponent found and initialized");
            }
        }
        else
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No AudioComponent found on this object - sounds will play in world space if configured");
            }
        }

        // Subscribe to user events
        ScenePrivate.User.Subscribe(User.AddUser, OnUserJoin);
        ScenePrivate.User.Subscribe(User.RemoveUser, OnUserLeave);

        // Subscribe to chat commands
        ScenePrivate.Chat.Subscribe(Chat.DefaultChannel, OnChat, true);
        
        // Initialize world cleaning state
        worldCleaningEnabled = AllowWorldCleanCommand;

        // Initialize dynamic grid sizes if enabled
        InitializeDynamicGridSizes();

        if (DebugMode)
        {
            Log.Write(LogLevel.Info, GetType().Name, $"Grid building system initialized with {validMeshCount} block types, grid size: {GridSize}");
            if (EnableDynamicGridSizes && availableGridSizes != null)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Dynamic grid sizes enabled: {string.Join(", ", availableGridSizes)}");
            }
        }

        // Get the Reactions interface
        reactions = ScenePrivate.Reactions;
        if (reactions != null && reactions.IsValid)
        {
            reactions.DisableDefaultReactions();
            // Add 8 custom reactions
            reactions.AddReaction("Grid.Swap", ReactionName1, ReactionCluster1);
            reactions.AddReaction("Grid.Cycle", ReactionName2, ReactionCluster2);
            reactions.AddReaction("Grid.Add", ReactionName3, ReactionCluster3);
            reactions.AddReaction("Grid.Remove", ReactionName4, ReactionCluster4);
            reactions.AddReaction("Grid.Tint", ReactionName5, ReactionCluster5);
            reactions.AddReaction("Grid.Copy", ReactionName6, ReactionCluster6);
            reactions.AddReaction("Grid.Mode7", ReactionName7, ReactionCluster7);
            reactions.AddReaction("Grid.Mode8", ReactionName8, ReactionCluster8);
            reactions.AddReaction("Grid.Mode9", ReactionName9, ReactionCluster9);
            // Subscribe to reaction events
            reactionSub = reactions.SubscribeToReaction(OnReaction, true);
        }

        // Find the RigidBodyComponent named 'ground_volume' on this object
        groundVolume = null;
        uint rbCount = ObjectPrivate.GetComponentCount(ComponentType.RigidBodyComponent);
        for (uint i = 0; i < rbCount; ++i)
        {
            var comp = ObjectPrivate.GetComponent(ComponentType.RigidBodyComponent, i) as RigidBodyComponent;
            if (comp != null && comp.Name == "ground_volume")
            {
                groundVolume = comp;
                break;
            }
        }
        if (groundVolume != null)
        {
            groundCenter = groundVolume.GetColliderCenter();
            groundExtents = groundVolume.GetColliderExtents();
        }
    }

    private void OnUserJoin(UserData userData)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(userData.User);
        if (agent?.Client == null) return;

        // Initialize user's block type to first mesh and mode to build (0)
        userBlockTypes[userData.User] = 0;
        userModes[userData.User] = 0; // 0 = BUILD mode
        
        // Initialize grid settings for new user
        userGridEnabled[userData.User] = true; // Grid starts enabled
        userGridSizeIndex[userData.User] = 0; // Start with first grid size
        userCurrentGridSize[userData.User] = GetCurrentGridSizeForUser(userData.User);

        // Subscribe to left mouse click (Trigger command)
        agent.Client.SubscribeToCommand("Trigger", CommandAction.Pressed, (CommandData data) =>
        {
            HandleBlockPlacement(agent, data);
        }, null);

        // Subscribe to key "1" for toggling swap mode
        agent.Client.SubscribeToCommand("Action1", CommandAction.Pressed, (CommandData data) =>
        {
            ToggleSwapMode(agent);
        }, null);

        // Subscribe to key "2" for cycling block types
        agent.Client.SubscribeToCommand("Action2", CommandAction.Pressed, (CommandData data) =>
        {
            CycleBlockType(agent);
        }, null);

        // Subscribe to key "3" for toggling between BUILD and DELETE modes
        agent.Client.SubscribeToCommand("Action3", CommandAction.Pressed, (CommandData data) =>
        {
            ToggleBuildDeleteMode(agent);
        }, null);

        // Subscribe to key "4" for cycling through all modes (BUILD -> DELETE -> ROTATE)
        agent.Client.SubscribeToCommand("Action4", CommandAction.Pressed, (CommandData data) =>
        {
            CycleMode(agent);
        }, null);

        // Subscribe to key "5" for toggling copy mode
        agent.Client.SubscribeToCommand("Action5", CommandAction.Pressed, (CommandData data) =>
        {
            ToggleCopyMode(agent);
        }, null);

        // Subscribe to key "6" for toggling tint mode
        agent.Client.SubscribeToCommand("Action6", CommandAction.Pressed, (CommandData data) =>
        {
            ToggleTintMode(agent);
        }, null);

        // Subscribe to key "0" for toggling grid on/off
        agent.Client.SubscribeToCommand("Action0", CommandAction.Pressed, (CommandData data) =>
        {
            ToggleGrid(agent);
        }, null);

        // Subscribe to key "9" for cycling grid sizes (if dynamic grid sizes enabled)
        if (EnableDynamicGridSizes)
        {
            agent.Client.SubscribeToCommand("Action9", CommandAction.Pressed, (CommandData data) =>
            {
                CycleGridSize(agent);
            }, null);
        }

        // Subscribe to key "7" for toggling paste tint mode
        agent.Client.SubscribeToCommand("Action7", CommandAction.Pressed, (CommandData data) =>
        {
            TogglePasteTintMode(agent);
        }, null);

        if (DebugMode)
        {
            Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} joined. Block type set to: {GetCurrentBlockTypeName(userData.User)}");
        }
    }

    private void OnUserLeave(UserData userData)
    {
        // Clean up user data
        userBlockTypes.Remove(userData.User);
        userModes.Remove(userData.User);
        userGridEnabled.Remove(userData.User);
        userGridSizeIndex.Remove(userData.User);
        userCurrentGridSize.Remove(userData.User);
        userLastPlacementTime.Remove(userData.User);
        userSelectedBlock.Remove(userData.User);
        userPreviousMode.Remove(userData.User);
        userHeldMoveKeys.Remove(userData.User);
        userMoveCoroutineActive.Remove(userData.User);
        userLastAction.Remove(userData.User);
    }

    private void HandleBlockPlacement(AgentPrivate agent, CommandData data)
    {
        try
        {
            // Get user's current mode
            int currentMode = 0; // Default to BUILD mode
            if (userModes.TryGetValue(agent.AgentInfo.SessionId, out currentMode))
            {
                // User mode is tracked
            }
            else
            {
                // Initialize to build mode
                userModes[agent.AgentInfo.SessionId] = 0;
                currentMode = 0;
            }

            switch (currentMode)
            {
                case 0: // BUILD mode
                    HandleBlockPlacementLogic(agent, data);
                    break;
                case 1: // DELETE mode
                    HandleBlockDeletion(agent, data);
                    break;
                case 2: // ROTATE mode
                    HandleBlockRotation(agent, data);
                    break;
                case 3: // COPY mode
                    HandleBlockCopy(agent, data);
                    break;
                case 4: // SWAP mode
                    HandleBlockSwap(agent, data);
                    break;
                case 5: // TINT mode
                    HandleBlockTint(agent, data);
                    break;
                case 6: // PASTE_TINT mode
                    HandlePasteTint(agent, data);
                    break;
                default:
                    // Fallback: do nothing in unknown mode
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleBlockPlacement: {ex.Message}");
        }
    }

    private void HandleBlockPlacementLogic(AgentPrivate agent, CommandData data)
    {
        // Check placement delay to prevent spam clicking
        if (BlockPlacementDelay > 0.0f)
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            DateTime currentTime = DateTime.UtcNow;
            
            if (userLastPlacementTime.TryGetValue(sessionId, out DateTime lastPlacementTime))
            {
                double timeSinceLastPlacement = (currentTime - lastPlacementTime).TotalSeconds;
                if (timeSinceLastPlacement < BlockPlacementDelay)
                {
                    // Too soon since last placement - ignore this request
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Placement blocked for {agent.AgentInfo.Name} - {timeSinceLastPlacement:F3}s since last placement (delay: {BlockPlacementDelay}s)");
                    }
                    return;
                }
            }
            
            // Update last placement time
            userLastPlacementTime[sessionId] = currentTime;
        }

        // Get the current block type for this user
        if (!userBlockTypes.TryGetValue(agent.AgentInfo.SessionId, out int blockTypeIndex))
        {
            blockTypeIndex = 0;
            userBlockTypes[agent.AgentInfo.SessionId] = blockTypeIndex;
        }

        // Validate block type index against total block count
        int totalBlockCount = GetTotalBlockCount();
        if (totalBlockCount == 0 || blockTypeIndex >= totalBlockCount)
        {
            blockTypeIndex = 0;
            userBlockTypes[agent.AgentInfo.SessionId] = blockTypeIndex;
        }

        // Get click position and normal from targeting data
        Vector targetPosition = data.TargetingPosition;
        Vector targetNormal = data.TargetingNormal;

        if (DebugMode)
        {
            Log.Write(LogLevel.Info, GetType().Name, $"Click detected at position: {targetPosition}, normal: {targetNormal}");
        }

        Vector gridPosition = Vector.Zero;
        bool clickedBlock = false;
        float gridSize = GetCurrentGridSizeForUser(agent.AgentInfo.SessionId);
        
        // If we clicked a block, use simple face normal for placement
        if (data.TargetingComponent != null)
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Clicked on block with component: {data.TargetingComponent}");
                Log.Write(LogLevel.Info, GetType().Name, $"placedBlocks keys: [{string.Join(", ", placedBlocks.Keys)}]");
            }
            // Find the clicked block
            foreach (var kvp in placedBlocks)
            {
                if (kvp.Value != null && kvp.Value.IsValid)
                {
                    foreach (var objectPrivate in kvp.Value.GetObjectPrivates())
                    {
                        if (objectPrivate != null && CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                        {
                            if (DebugMode)
                            {
                                Log.Write(LogLevel.Info, GetType().Name, $"Found clicked block at grid key: {kvp.Key}");
                            }
                            // Use the center of the clicked block's grid cell as the base
                            string clickedGridKey = kvp.Key;
                            float clickedBlockGridSize = gridSize;
                            if (blockGridSizes.TryGetValue(clickedGridKey, out float storedSize)) {
                                clickedBlockGridSize = storedSize;
                            }
                            Vector clickedBlockCenter = GetPositionFromGridKey(clickedGridKey, clickedBlockGridSize);
                            Vector placementDir = targetNormal;
                            Vector offset = placementDir * clickedBlockGridSize;
                            Vector offsetPos = clickedBlockCenter + offset;
                            // Snap to grid
                            gridPosition = SnapToGrid(offsetPos, clickedBlockGridSize);
                            if (DebugMode)
                            {
                                Log.Write(LogLevel.Info, GetType().Name, $"Face normal: {targetNormal}, offset: {offset}, gridPosition: {gridPosition}");
                                Log.Write(LogLevel.Info, GetType().Name, $"Clicked block center: {clickedBlockCenter}, clickedBlockGridSize: {clickedBlockGridSize}");
                            }
                            clickedBlock = true;
                            break;
                        }
                    }
                    if (clickedBlock) break;
                }
            }
            // Prevent placement at (0,0,0) if block not found
            if (!clickedBlock) {
                if (DebugMode) {
                    Log.Write(LogLevel.Info, GetType().Name, "Clicked block not found in placedBlocks. Treating as ground click.");
                }
                // Do not return; let the code continue to the ground placement logic below
            }
        }
        
        if (!clickedBlock)
        {
            // If not clicking a block, treat as ground or free space
            Vector groundPos = targetPosition;
            float blockHalfHeight = gridSize * 0.5f; // Default to grid size if block height is unknown
            if (groundVolume != null)
            {
                float groundTopY = groundCenter.Y + groundExtents.Y;
                groundPos.Y = groundTopY + blockHalfHeight;
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, $"Placing block on ground_volume: groundTopY={groundTopY}, blockHalfHeight={blockHalfHeight}, finalY={groundPos.Y}");
                }
            }
            else
            {
                // Fallback: Use the actual click Y instead of forcing a fixed Y value
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "ground_volume not found, using clicked Y position");
                }
            }
            gridPosition = SnapToGrid(groundPos, gridSize);
        }

        // Validate placement height
        if (gridPosition.Y > MaxHeight)
        {
            agent.SendChat("Cannot place blocks above maximum height limit.");
            return;
        }

        // Check if position is already occupied
        string gridKey = GetGridKey(gridPosition, gridSize);
        if (DebugMode)
        {
            Log.Write(LogLevel.Info, GetType().Name, $"Placing block at position: {gridPosition}, gridSize: {gridSize}, gridKey: {gridKey}");
        }
        if (placedBlocks.ContainsKey(gridKey))
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Position {gridKey} already occupied");
            }
            return;
        }

        // Place the block
        PlaceBlock(gridPosition, blockTypeIndex, agent, gridSize, targetNormal);
    }

    private void PlaceBlock(Vector position, int blockTypeIndex, AgentPrivate agent, float gridSize, Vector placementNormal)
    {
        try
        {
            // Get block from either list using helper method
            ClusterResource blockMesh = GetBlockByIndex(blockTypeIndex);
            if (blockMesh == null)
            {
                Log.Write(LogLevel.Error, GetType().Name, $"Invalid block type index: {blockTypeIndex}");
                return;
            }

            // Create the block with copied rotation (if any) or no rotation
            Quaternion rotation = Quaternion.Identity;
            if (userCopiedRotations.TryGetValue(agent.AgentInfo.SessionId, out Quaternion copiedRot)) {
                rotation = copiedRot;
            }
            ScenePrivate.CreateCluster(blockMesh, position, rotation, Vector.Zero, (ScenePrivate.CreateClusterData data) =>
            {
                if (data.Success && data.ClusterReference != null)
                {
                    string gridKey = GetGridKey(position, gridSize);
                    placedBlocks[gridKey] = data.ClusterReference;
                    blockGridSizes[gridKey] = gridSize;
                    
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Added block to placedBlocks dictionary at key: {gridKey}, total blocks: {placedBlocks.Count}");
                    }
                    
                    // Initialize block rotation tracking with the rotation used during creation
                    blockRotations[gridKey] = rotation;
                    
                    // Track the block type for copying
                    blockTypes[gridKey] = blockTypeIndex;

                    // Play placement sound for the specific agent
                    PlaySound(PlaceBlockSound, agent);

                    // Trigger emissive pulse effect if enabled and block has emissive materials
                    if (EmissivePulseDuration > 0.0f)
                    {
                        TriggerEmissivePulse(data.ClusterReference, gridKey);
                    }

                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Block placed at {gridKey} by {agent.AgentInfo.Name}");
                    }

                    // Apply copied tints if available
                    if (userCopiedTints.TryGetValue(agent.AgentInfo.SessionId, out List<Color> tints) && tints != null && tints.Count > 0)
                    {
                        int tintIndex = 0;
                        foreach (var objectPrivate in data.ClusterReference.GetObjectPrivates())
                        {
                            if (objectPrivate != null && objectPrivate.TryGetFirstComponent(out MeshComponent mesh))
                            {
                                if (mesh.IsScriptable)
                                {
                                    foreach (var mat in mesh.GetRenderMaterials())
                                    {
                                        if (mat.HasTint && tintIndex < tints.Count)
                                        {
                                            var props = mat.GetProperties();
                                            props.Tint = tints[tintIndex++];
                                            mat.SetProperties(props);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    Log.Write(LogLevel.Error, GetType().Name, $"Failed to create block: {data.Message}");
                }
            });
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in PlaceBlock: {ex.Message}");
        }
    }

    private void CycleBlockType(AgentPrivate agent)
    {
        SessionId sessionId = agent.AgentInfo.SessionId;
        // Cycle block type as before
        int currentType = 0;
        if (userBlockTypes.TryGetValue(sessionId, out currentType))
        {
            int validMeshCount = GetValidMeshCount();
            if (validMeshCount > 0)
            {
                currentType = GetNextValidBlockIndex(currentType);
                userBlockTypes[sessionId] = currentType;
            }
        }
        else
        {
            userBlockTypes[sessionId] = 0;
        }
        // Always set to BUILD mode after cycling
        userModes[sessionId] = 0;
        // Send chat about entering cycle mode
        agent.SendChat("Block has changed, click to place the new block");
    }

    private string GetCurrentBlockTypeName(SessionId sessionId)
    {
        if (!userBlockTypes.TryGetValue(sessionId, out int blockType))
        {
            return "Unknown";
        }

        ClusterResource mesh = GetBlockByIndex(blockType);
        if (mesh?.ResourceId != null)
        {
            return mesh.ResourceId.ToString();
        }

        return $"Block {blockType + 1}";
    }

    private void ToggleBuildDeleteMode(AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get current mode or initialize to build mode
            int currentMode = 0;
            if (userModes.TryGetValue(sessionId, out currentMode))
            {
                // Toggle between BUILD (0) and DELETE (1) only
                currentMode = (currentMode == 0) ? 1 : 0;
            }
            else
            {
                // Initialize to delete mode (toggling from default build mode)
                currentMode = 1;
            }
            
            userModes[sessionId] = currentMode;
            
            // Notify user of the mode change
            string modeText = GetModeText(currentMode);
            agent.SendChat($"Switched to {modeText}");
            
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} switched to {modeText}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in ToggleBuildDeleteMode: {ex.Message}");
        }
    }

    private void CycleMode(AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get current mode or initialize to build mode
            int currentMode = 0;
            if (userModes.TryGetValue(sessionId, out currentMode))
            {
                // Toggle between BUILD (0) and ROTATE (2) only
                if (currentMode == 0) // BUILD mode
                {
                    currentMode = 2; // Switch to ROTATE mode
                }
                else // Any other mode (DELETE or ROTATE)
                {
                    currentMode = 0; // Switch to BUILD mode
                }
            }
            else
            {
                // Initialize to rotate mode (toggling from default build mode)
                currentMode = 2;
            }
            
            userModes[sessionId] = currentMode;
            
            // Notify user of the mode change
            string modeText = GetModeText(currentMode);
            agent.SendChat($"Switched to {modeText}");
            
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} switched to {modeText}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in CycleMode: {ex.Message}");
        }
    }


    private string GetModeText(int mode)
    {
        switch (mode)
        {
            case 0: return "BUILD MODE";
            case 1: return "DELETE MODE";
            case 2: return "ROTATE MODE";
            case 3: return "COPY MODE";
            case 4: return "SWAP MODE";
            case 5: return "TINT MODE";
            default: return "UNKNOWN MODE";
        }
    }

    private void ToggleCopyMode(AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get current mode or initialize to build mode
            int currentMode = 0;
            if (userModes.TryGetValue(sessionId, out currentMode))
            {
                // Toggle between BUILD (0) and COPY (3) only
                if (currentMode == 3) // COPY mode
                {
                    currentMode = 0; // Switch to BUILD mode
                }
                else // Any other mode
                {
                    currentMode = 3; // Switch to COPY mode
                }
            }
            else
            {
                // Initialize to copy mode (toggling from default build mode)
                currentMode = 3;
            }
            
            userModes[sessionId] = currentMode;
            
            // Notify user of the mode change
            string modeText = GetModeText(currentMode);
            if (currentMode == 3)
            {
                agent.SendChat($"Switched to {modeText} - Click on a block to copy it");
            }
            else
            {
                agent.SendChat($"Switched to {modeText}");
            }
            
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} switched to {modeText}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in ToggleCopyMode: {ex.Message}");
        }
    }

    private void ToggleSwapMode(AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get current mode or initialize to build mode
            int currentMode = 0;
            if (userModes.TryGetValue(sessionId, out currentMode))
            {
                // Toggle between BUILD (0) and SWAP (4) only
                if (currentMode == 4) // SWAP mode
                {
                    currentMode = 0; // Switch to BUILD mode
                }
                else // Any other mode
                {
                    currentMode = 4; // Switch to SWAP mode
                }
            }
            else
            {
                // Initialize to swap mode (toggling from default build mode)
                currentMode = 4;
            }
            
            userModes[sessionId] = currentMode;
            
            // Notify user of the mode change
            string modeText = GetModeText(currentMode);
            if (currentMode == 4)
            {
                agent.SendChat($"Switched to {modeText} - Click on a block to cycle through types");
            }
            else
            {
                agent.SendChat($"Switched to {modeText}");
            }
            
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} switched to {modeText}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in ToggleSwapMode: {ex.Message}");
        }
    }

    private void HandleBlockCopy(AgentPrivate agent, CommandData data)
    {
        try
        {
            // Use the targeting data to find which block was clicked
            if (data.TargetingComponent == null)
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for copying");
                }
                return;
            }

            // Find the block that was clicked
            string blockToCopy = null;
            
            foreach (var kvp in placedBlocks)
            {
                if (kvp.Value != null && kvp.Value.IsValid)
                {
                    // Check if this cluster contains the targeted component
                    var objectPrivates = kvp.Value.GetObjectPrivates();
                    foreach (var objectPrivate in objectPrivates)
                    {
                        if (objectPrivate != null)
                        {
                            // Check all component types for a matching ComponentId
                            if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                            {
                                blockToCopy = kvp.Key;
                                break;
                            }
                        }
                    }
                    if (blockToCopy != null) break;
                }
            }

            if (blockToCopy != null)
            {
                CopyBlockType(blockToCopy, agent);
            }
            else
            {
                agent.SendChat("No placed block found to copy. Click on a block you've placed.");
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No placed block found to copy at clicked position");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleBlockCopy: {ex.Message}");
        }
    }

    private void CopyBlockType(string gridKey, AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get the block type from our tracking dictionary
            if (blockTypes.TryGetValue(gridKey, out int blockTypeIndex))
            {
                // Validate the block type index using helper method
                ClusterResource blockResource = GetBlockByIndex(blockTypeIndex);
                if (blockResource != null)
                {
                    // Set the user's current block type to the copied one
                    userBlockTypes[sessionId] = blockTypeIndex;
                    
                    // Copy the rotation state if the block was rotated
                    Quaternion copiedRotation = Quaternion.Identity;
                    if (blockRotations.TryGetValue(gridKey, out copiedRotation))
                    {
                        userCopiedRotations[sessionId] = copiedRotation;
                        if (DebugMode)
                        {
                            Log.Write(LogLevel.Info, GetType().Name, $"Copied rotation for block at {gridKey}: {copiedRotation}");
                        }
                    }
                    else
                    {
                        // Clear any previous copied rotation if the block wasn't rotated
                        userCopiedRotations.Remove(sessionId);
                    }
                    
                    // Get the block type name for feedback
                    string blockTypeName = GetCurrentBlockTypeName(sessionId);
                    agent.SendChat($"Copied block type: {blockTypeName}");
                    
                    // Automatically switch to BUILD mode after copying
                    userModes[sessionId] = 0;
                    agent.SendChat("Switched to BUILD MODE - You can now place the copied block");
                    
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} copied block type {blockTypeIndex}: {blockTypeName}");
                    }

                    // Extract tint values from the copied block's materials
                    List<Color> copiedTints = new List<Color>();
                    if (placedBlocks.TryGetValue(gridKey, out Cluster cluster) && cluster != null && cluster.IsValid)
                    {
                        foreach (var objectPrivate in cluster.GetObjectPrivates())
                        {
                            if (objectPrivate != null && objectPrivate.TryGetFirstComponent(out MeshComponent mesh))
                            {
                                if (mesh.IsScriptable)
                                {
                                    foreach (var mat in mesh.GetRenderMaterials())
                                    {
                                        if (mat.HasTint)
                                        {
                                            var props = mat.GetProperties();
                                            copiedTints.Add(props.Tint);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    userCopiedTints[sessionId] = copiedTints;

                    Quaternion copiedRot = Quaternion.Identity;
                    if (blockRotations.TryGetValue(gridKey, out copiedRot))
                        userCopiedRotations[sessionId] = copiedRot;
                    else
                        userCopiedRotations.Remove(sessionId);
                }
                else
                {
                    agent.SendChat("Error: Copied block type is no longer valid");
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Warning, GetType().Name, $"Invalid block type index {blockTypeIndex} for copied block at {gridKey}");
                    }
                }
            }
            else
            {
                agent.SendChat("Error: Cannot determine block type to copy");
                if (DebugMode)
                {
                    Log.Write(LogLevel.Warning, GetType().Name, $"No block type found for grid key {gridKey}");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in CopyBlockType: {ex.Message}");
            agent.SendChat($"Error copying block: {ex.Message}");
        }
    }

    private void HandleBlockSwap(AgentPrivate agent, CommandData data)
    {
        try
        {
            // Use the targeting data to find which block was clicked
            if (data.TargetingComponent == null)
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for swapping");
                }
                return;
            }

            // Find the block that was clicked
            string blockToSwap = null;
            
            foreach (var kvp in placedBlocks)
            {
                if (kvp.Value != null && kvp.Value.IsValid)
                {
                    // Check if this cluster contains the targeted component
                    var objectPrivates = kvp.Value.GetObjectPrivates();
                    foreach (var objectPrivate in objectPrivates)
                    {
                        if (objectPrivate != null)
                        {
                            // Check all component types for a matching ComponentId
                            if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                            {
                                blockToSwap = kvp.Key;
                                break;
                            }
                        }
                    }
                    if (blockToSwap != null) break;
                }
            }

            if (blockToSwap != null)
            {
                SwapBlockType(blockToSwap, agent);
            }
            else
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No placed block found to swap at clicked position");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleBlockSwap: {ex.Message}");
        }
    }

    private void SwapBlockType(string gridKey, AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            
            // Get the current block type from our tracking dictionary
            if (blockTypes.TryGetValue(gridKey, out int currentBlockTypeIndex))
            {
                // Get the position and rotation of the current block
                float blockGridSize = 1.0f; // fallback value
                if (blockGridSizes.TryGetValue(gridKey, out float storedSize)) {
                    blockGridSize = storedSize;
                }
                Vector blockPosition = GetPositionFromGridKey(gridKey, blockGridSize);
                Quaternion currentRotation = Quaternion.Identity;
                if (blockRotations.TryGetValue(gridKey, out currentRotation))
                {
                    // Use the stored rotation
                }
                
                // Get the current block cluster
                if (placedBlocks.TryGetValue(gridKey, out Cluster currentBlockCluster))
                {
                    // Cycle to next block type, skipping null entries
                    int totalBlockCount = GetTotalBlockCount();
                    if (totalBlockCount == 0)
                    {
                        // No block objects configured
                        return;
                    }

                    // Use helper method to get next valid block index
                    int newTypeIndex = GetNextValidBlockIndex(currentBlockTypeIndex);
                    
                    // Get the new block resource
                    ClusterResource newBlockResource = GetBlockByIndex(newTypeIndex);
                    if (newBlockResource != null)
                    {
                        // Destroy the current block
                        currentBlockCluster.Destroy();
                        
                        // Remove from tracking dictionaries
                        placedBlocks.Remove(gridKey);
                        blockRotations.Remove(gridKey);
                        blockTypes.Remove(gridKey);
                        
                        // Create the new block at the same position and rotation
                        Quaternion newBlockRotation = currentRotation;
                        ScenePrivate.CreateCluster(newBlockResource, blockPosition, newBlockRotation, Vector.Zero, (ScenePrivate.CreateClusterData data) =>
                        {
                            if (data.Success && data.ClusterReference != null)
                            {
                                // Add to tracking dictionaries
                                placedBlocks[gridKey] = data.ClusterReference;
                                blockRotations[gridKey] = newBlockRotation;
                                blockTypes[gridKey] = newTypeIndex;
                                
                                // Trigger emissive pulse if enabled
                                if (EmissivePulseDuration > 0)
                                {
                                    TriggerEmissivePulse(data.ClusterReference, gridKey);
                                }
                                
                                // Play placement sound
                                PlaySound(PlaceBlockSound, agent);
                                
                                if (DebugMode)
                                {
                                    string blockTypeName = GetCurrentBlockTypeName(sessionId);
                                    int validMeshCount = GetValidMeshCount();
                                    Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} swapped block at {gridKey} to type {newTypeIndex}: {blockTypeName} ({newTypeIndex + 1}/{validMeshCount} valid)");
                                }

                                // In SwapBlockType, after creating the new block (inside the callback, after block is placed), add:
                                userCopiedTints.Remove(agent.AgentInfo.SessionId);
                            }
                            else
                            {
                                if (DebugMode)
                                {
                                    Log.Write(LogLevel.Error, GetType().Name, $"Failed to create new block cluster for type {newTypeIndex}: {data.Message}");
                                }
                            }
                        });
                    }
                    else
                    {
                        if (DebugMode)
                        {
                            Log.Write(LogLevel.Warning, GetType().Name, $"Invalid block type index {newTypeIndex}");
                        }
                    }
                }
                else
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Warning, GetType().Name, $"No block cluster found for grid key {gridKey}");
                    }
                }
            }
            else
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Warning, GetType().Name, $"No block type found for grid key {gridKey}");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in SwapBlockType: {ex.Message}");
        }
    }

    private void HandleBlockDeletion(AgentPrivate agent, CommandData data)
    {
        // Use the targeting data directly to find which block was clicked
        if (data.TargetingComponent == null)
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for deletion");
            }
            return;
        }

        // Find the block that was clicked by checking all placed blocks
        string blockToDelete = null;
        foreach (var kvp in placedBlocks)
        {
            if (kvp.Value != null && kvp.Value.IsValid)
            {
                // Check if this cluster contains the targeted component
                var objectPrivates = kvp.Value.GetObjectPrivates();
                foreach (var objectPrivate in objectPrivates)
                {
                    if (objectPrivate != null)
                    {
                        // Check all component types for a matching ComponentId
                        if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                        {
                            blockToDelete = kvp.Key;
                            break;
                        }
                    }
                }
                if (blockToDelete != null) break;
            }
        }

        if (blockToDelete != null)
        {
            DeleteBlock(blockToDelete, agent);
        }
        else
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No block found to delete at clicked position");
            }
        }
    }

    private void DeleteBlock(string gridKey, AgentPrivate agent)
    {
        try
        {
            if (placedBlocks.TryGetValue(gridKey, out Cluster blockCluster))
            {
                if (blockCluster != null && blockCluster.IsValid)
                {
                    // Remove the block from the scene
                    blockCluster.Destroy();
                    
                    // Remove from our tracking dictionaries
                    placedBlocks.Remove(gridKey);
                    blockRotations.Remove(gridKey);
                    blockTypes.Remove(gridKey);
                    
                    // Play deletion sound for the specific agent
                    PlaySound(DeleteBlockSound, agent);
                    
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Block deleted at {gridKey} by {agent.AgentInfo.Name}");
                    }
                }
                else
                {
                    // Clean up invalid reference
                    placedBlocks.Remove(gridKey);
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Cleaned up invalid block reference at {gridKey}");
                    }
                }
            }
            else
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, $"No block found at {gridKey} to delete");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in DeleteBlock: {ex.Message}");
        }
    }

    private void HandleBlockRotation(AgentPrivate agent, CommandData data)
    {
        try
        {
            // Use the targeting data to find which block was clicked
            if (data.TargetingComponent == null)
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for rotation");
                }
                return;
            }

            // Find the block that was clicked
            string blockToRotate = null;
            Cluster targetCluster = null;
            
            foreach (var kvp in placedBlocks)
            {
                if (kvp.Value != null && kvp.Value.IsValid)
                {
                    // Check if this cluster contains the targeted component
                    var objectPrivates = kvp.Value.GetObjectPrivates();
                    foreach (var objectPrivate in objectPrivates)
                    {
                        if (objectPrivate != null)
                        {
                            // Check all component types for a matching ComponentId
                            if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                            {
                                blockToRotate = kvp.Key;
                                targetCluster = kvp.Value;
                                break;
                            }
                        }
                    }
                    if (blockToRotate != null) break;
                }
            }

            if (blockToRotate != null && targetCluster != null)
            {
                RotateBlock(blockToRotate, targetCluster, data.TargetingNormal, agent);
            }
            else
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, "No block found to rotate at clicked position");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleBlockRotation: {ex.Message}");
        }
    }

    private void RotateBlock(string gridKey, Cluster blockCluster, Vector clickedNormal, AgentPrivate agent)
    {
        try
        {
            // Get current rotation or initialize to identity
            Quaternion currentRotation = Quaternion.Identity;
            if (blockRotations.TryGetValue(gridKey, out currentRotation))
            {
                // Current rotation is tracked
            }
            else
            {
                // Initialize rotation tracking
                blockRotations[gridKey] = Quaternion.Identity;
                currentRotation = Quaternion.Identity;
            }

            // Determine rotation axis based on clicked face normal
            Quaternion rotationDelta;
            string rotationDescription;

            // Normalize the normal vector to handle floating point precision
            Vector normal = clickedNormal.Normalized();
            
            // Check if clicked on top or bottom face (Y-axis dominant)
            if (Math.Abs(normal.Y) > 0.7f) // Threshold for detecting Y-dominant normals
            {
                // Vertical face clicked - rotate around Y-axis (horizontal rotation)
                Vector eulerAngles = new Vector(0, 90 * (float)(Math.PI / 180.0), 0);
                rotationDelta = Quaternion.FromEulerAngles(eulerAngles);
                rotationDescription = "horizontally";
            }
            // Check if clicked on side faces (X or Z dominant)
            else if (Math.Abs(normal.X) > Math.Abs(normal.Z))
            {
                // X-dominant face - rotate around Z-axis
                Vector eulerAngles = new Vector(0, 0, 90 * (float)(Math.PI / 180.0));
                rotationDelta = Quaternion.FromEulerAngles(eulerAngles);
                rotationDescription = "around Z-axis";
            }
            else
            {
                // Z-dominant face - rotate around X-axis
                Vector eulerAngles = new Vector(90 * (float)(Math.PI / 180.0), 0, 0);
                rotationDelta = Quaternion.FromEulerAngles(eulerAngles);
                rotationDescription = "around X-axis";
            }

            // Apply the rotation
            Quaternion newRotation = currentRotation * rotationDelta;
            blockRotations[gridKey] = newRotation;

            // Apply rotation to all objects in the cluster using Mover
            var objectPrivates = blockCluster.GetObjectPrivates();
            bool anyRotated = false;
            bool anyNonMovable = false;
            
            foreach (var objectPrivate in objectPrivates)
            {
                if (objectPrivate != null)
                {
                    // Check if the object is movable (keyframed) before attempting rotation
                    if (objectPrivate.IsMovable)
                    {
                        try
                        {
                            // Use AddRotateOffset for relative rotation
                            objectPrivate.Mover.AddRotateOffset(rotationDelta);
                            anyRotated = true;
                            if (DebugMode)
                            {
                                Log.Write(LogLevel.Info, GetType().Name, $"Applied rotation to movable object in cluster at {gridKey}");
                            }
                        }
                        catch (Exception ex)
                        {
                            // This should rarely happen now that we check IsMovable
                            if (DebugMode)
                            {
                                Log.Write(LogLevel.Warning, GetType().Name, $"Failed to rotate movable object: {ex.Message}");
                            }
                        }
                    }
                    else
                    {
                        anyNonMovable = true;
                        if (DebugMode)
                        {
                            Log.Write(LogLevel.Info, GetType().Name, $"Skipped non-movable object in cluster at {gridKey}");
                        }
                    }
                }
            }
              // Provide appropriate feedback based on what happened
            if (anyRotated && anyNonMovable)
            {
                // agent.SendChat($"Block partially rotated {rotationDescription} (some objects are not keyframed)");
            }
            else if (anyRotated)
            {
                // agent.SendChat($"Block rotated {rotationDescription}");
            }
            else if (anyNonMovable)
            {
                agent.SendChat("Cannot rotate block - objects are not keyframed (set Motion Type to 'Keyframed')");
                return;
            }
            else
            {
                agent.SendChat("No objects found to rotate");
                return;
            }
            
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Block at {gridKey} rotated {rotationDescription} by {agent.AgentInfo.Name}. Normal: {normal}");
            }
            // After setting rotationDescription in RotateBlock, add:
            agent.SendChat($"[DEBUG] Rotating {rotationDescription}");
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in RotateBlock: {ex.Message}");
            agent.SendChat($"Error rotating block: {ex.Message}");
        }
    }

    private void PlaySound(SoundResource sound, AgentPrivate agent)
    {
        try
        {
            if (sound != null && sound.IsValid && agent != null)
            {
                // Create play settings with volume control
                PlaySettings playSettings = PlaySettings.PlayOnce;
                playSettings.Loudness = SoundVolume / 100.0f; // Convert 0-100 to 0-1
                
                // Play sound directly to the specific agent only
                PlayHandle agentPlayHandle = agent.PlaySound(sound, playSettings);
                
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, $"Sound played to agent {agent.AgentInfo.Name} with volume: {SoundVolume}%");
                }
            }
            else
            {
                if (DebugMode)
                {
                    if (sound == null)
                        Log.Write(LogLevel.Info, GetType().Name, "Sound resource is null - skipping sound playback");
                    else if (!sound.IsValid)
                        Log.Write(LogLevel.Info, GetType().Name, "Sound resource is not valid - skipping sound playback");
                    else if (agent == null)
                        Log.Write(LogLevel.Info, GetType().Name, "Agent is null - skipping sound playback");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in PlaySound: {ex.Message}");
        }
    }

    private bool CheckObjectForTargetComponent(ObjectPrivate objectPrivate, ComponentId targetComponentId)
    {
        try
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Checking object {objectPrivate.ObjectId} against target component {targetComponentId}");
            }
            
            // Check RigidBodyComponent
            uint rigidBodyCount = objectPrivate.GetComponentCount(ComponentType.RigidBodyComponent);
            for (uint i = 0; i < rigidBodyCount; i++)
            {
                var component = objectPrivate.GetComponent(ComponentType.RigidBodyComponent, i) as RigidBodyComponent;
                if (component != null && component.ComponentId.Equals(targetComponentId))
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Found matching RigidBodyComponent: {component.ComponentId}");
                    }
                    return true;
                }
            }

            // Check MeshComponent
            uint meshCount = objectPrivate.GetComponentCount(ComponentType.MeshComponent);
            for (uint i = 0; i < meshCount; i++)
            {
                var component = objectPrivate.GetComponent(ComponentType.MeshComponent, i) as MeshComponent;
                if (component != null && component.ComponentId.Equals(targetComponentId))
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Found matching MeshComponent: {component.ComponentId}");
                    }
                    return true;
                }
            }

            // Check AnimationComponent
            uint animationCount = objectPrivate.GetComponentCount(ComponentType.AnimationComponent);
            for (uint i = 0; i < animationCount; i++)
            {
                var component = objectPrivate.GetComponent(ComponentType.AnimationComponent, i) as AnimationComponent;
                if (component != null && component.ComponentId.Equals(targetComponentId))
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Found matching AnimationComponent: {component.ComponentId}");
                    }
                    return true;
                }
            }

            // Check LightComponent
            uint lightCount = objectPrivate.GetComponentCount(ComponentType.LightComponent);
            for (uint i = 0; i < lightCount; i++)
            {
                var component = objectPrivate.GetComponent(ComponentType.LightComponent, i) as LightComponent;
                if (component != null && component.ComponentId.Equals(targetComponentId))
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Found matching LightComponent: {component.ComponentId}");
                    }
                    return true;
                }
            }

            // Check AudioComponent
            uint audioCount = objectPrivate.GetComponentCount(ComponentType.AudioComponent);
            for (uint i = 0; i < audioCount; i++)
            {
                var component = objectPrivate.GetComponent(ComponentType.AudioComponent, i) as AudioComponent;
                if (component != null && component.ComponentId.Equals(targetComponentId))
                {
                    if (DebugMode)
                    {
                        Log.Write(LogLevel.Info, GetType().Name, $"Found matching AudioComponent: {component.ComponentId}");
                    }
                    return true;
                }
            }

            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"No matching component found for object {objectPrivate.ObjectId}");
            }
            return false;
        }
        catch (Exception ex)
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Error, GetType().Name, $"Error in CheckObjectForTargetComponent: {ex.Message}");
            }
            return false;
        }
    }

    private string GetGridKey(Vector position, float gridSize)
    {
        int x = (int)Math.Round(position.X / gridSize);
        int y = (int)Math.Round(position.Y / gridSize);
        int z = (int)Math.Round(position.Z / gridSize);
        return $"{x},{y},{z}";
    }

    // Helper method to get a block from any list by index
    private ClusterResource GetBlockByIndex(int index)
    {
        if (BlockObjects != null && index < BlockObjects.Count)
            return BlockObjects[index];
        int offset = BlockObjects?.Count ?? 0;
        if (BlockObjects2 != null && index >= offset && index < offset + BlockObjects2.Count)
            return BlockObjects2[index - offset];
        offset += BlockObjects2?.Count ?? 0;
        if (BlockObjects3 != null && index >= offset && index < offset + BlockObjects3.Count)
            return BlockObjects3[index - offset];
        offset += BlockObjects3?.Count ?? 0;
        if (BlockObjects4 != null && index >= offset && index < offset + BlockObjects4.Count)
            return BlockObjects4[index - offset];
        offset += BlockObjects4?.Count ?? 0;
        if (BlockObjects5 != null && index >= offset && index < offset + BlockObjects5.Count)
            return BlockObjects5[index - offset];
        offset += BlockObjects5?.Count ?? 0;
        if (BlockObjects6 != null && index >= offset && index < offset + BlockObjects6.Count)
            return BlockObjects6[index - offset];
        offset += BlockObjects6?.Count ?? 0;
        if (BlockObjects7 != null && index >= offset && index < offset + BlockObjects7.Count)
            return BlockObjects7[index - offset];
        offset += BlockObjects7?.Count ?? 0;
        if (BlockObjects8 != null && index >= offset && index < offset + BlockObjects8.Count)
            return BlockObjects8[index - offset];
        return null;
    }
    // Helper method to get total count of blocks from all lists
    private int GetTotalBlockCount()
    {
        int count = 0;
        if (BlockObjects != null) count += BlockObjects.Count;
        if (BlockObjects2 != null) count += BlockObjects2.Count;
        if (BlockObjects3 != null) count += BlockObjects3.Count;
        if (BlockObjects4 != null) count += BlockObjects4.Count;
        if (BlockObjects5 != null) count += BlockObjects5.Count;
        if (BlockObjects6 != null) count += BlockObjects6.Count;
        if (BlockObjects7 != null) count += BlockObjects7.Count;
        if (BlockObjects8 != null) count += BlockObjects8.Count;
        return count;
    }
    // Helper method to count valid (non-null) meshes from all lists
    private int GetValidMeshCount()
    {
        int validCount = 0;
        if (BlockObjects != null)
            foreach (var mesh in BlockObjects)
                if (mesh != null) validCount++;
        if (BlockObjects2 != null)
            foreach (var mesh in BlockObjects2)
                if (mesh != null) validCount++;
        if (BlockObjects3 != null)
            foreach (var mesh in BlockObjects3)
                if (mesh != null) validCount++;
        if (BlockObjects4 != null)
            foreach (var mesh in BlockObjects4)
                if (mesh != null) validCount++;
        if (BlockObjects5 != null)
            foreach (var mesh in BlockObjects5)
                if (mesh != null) validCount++;
        if (BlockObjects6 != null)
            foreach (var mesh in BlockObjects6)
                if (mesh != null) validCount++;
        if (BlockObjects7 != null)
            foreach (var mesh in BlockObjects7)
                if (mesh != null) validCount++;
        if (BlockObjects8 != null)
            foreach (var mesh in BlockObjects8)
                if (mesh != null) validCount++;
        return validCount;
    }

    // Helper method to get the next valid block index (for cycling)
    private int GetNextValidBlockIndex(int currentIndex)
    {
        int totalCount = GetTotalBlockCount();
        if (totalCount == 0) return 0;
        int originalIndex = currentIndex;
        do
        {
            currentIndex = (currentIndex + 1) % totalCount;
            ClusterResource block = GetBlockByIndex(currentIndex);
            if (block != null) return currentIndex;
        }
        while (currentIndex != originalIndex);
        return currentIndex; // Return original if no valid blocks found
    }
    private void OnChat(ChatData data)
    {
        if (!AllowWorldCleanCommand) return;
        if (data == null || string.IsNullOrWhiteSpace(data.Message)) return;
        string[] commands = data.Message.Trim().Split(' ');
        if (commands.Length == 0) return;
        string command = commands[0].ToLower();
        if (command == "clean" && commands.Length >= 2 && commands[1].ToLower() == "world")
        {
            HandleCleanWorldCommand(data);
            return;
        }
    }

    private void InitializeDynamicGridSizes()
    {
        try
        {
            if (!EnableDynamicGridSizes || string.IsNullOrWhiteSpace(DynamicGridSizesList))
            {
                availableGridSizes = null;
                return;
            }
            var sizes = DynamicGridSizesList.Split(',');
            List<float> parsedSizes = new List<float>();
            foreach (var s in sizes)
            {
                if (float.TryParse(s.Trim(), out float val) && val > 0.01f)
                {
                    parsedSizes.Add(val);
                }
            }
            if (parsedSizes.Count > 0)
            {
                availableGridSizes = parsedSizes.ToArray();
            }
            else
            {
                availableGridSizes = null;
                if (DebugMode)
                {
                    Log.Write(LogLevel.Warning, GetType().Name, "No valid dynamic grid sizes parsed from list");
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in InitializeDynamicGridSizes: {ex.Message}");
        }
    }

    private float GetCurrentGridSizeForUser(SessionId sessionId)
    {
        if (!EnableDynamicGridSizes || availableGridSizes == null || availableGridSizes.Length == 0)
        {
            return GridSize;
        }
        if (userGridSizeIndex.TryGetValue(sessionId, out int index) && index >= 0 && index < availableGridSizes.Length)
        {
            return availableGridSizes[index];
        }
        return availableGridSizes[0];
    }

    private void ToggleGrid(AgentPrivate agent)
    {
        try
        {
            SessionId sessionId = agent.AgentInfo.SessionId;
            bool currentState = true;
            if (userGridEnabled.TryGetValue(sessionId, out currentState))
            {
                userGridEnabled[sessionId] = !currentState;
            }
            else
            {
                userGridEnabled[sessionId] = false;
            }
            string stateText = userGridEnabled[sessionId] ? "ENABLED" : "DISABLED";
            agent.SendChat($"Grid snapping {stateText}");
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} toggled grid to {stateText}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in ToggleGrid: {ex.Message}");
        }
    }

    private void CycleGridSize(AgentPrivate agent)
    {
        try
        {
            if (!EnableDynamicGridSizes || availableGridSizes == null || availableGridSizes.Length <= 1)
            {
                agent.SendChat("Dynamic grid sizes not enabled or only one size available.");
                return;
            }
            SessionId sessionId = agent.AgentInfo.SessionId;
            int currentIndex = 0;
            if (userGridSizeIndex.TryGetValue(sessionId, out currentIndex))
            {
                currentIndex = (currentIndex + 1) % availableGridSizes.Length;
            }
            else
            {
                currentIndex = 0;
            }
            userGridSizeIndex[sessionId] = currentIndex;
            float newSize = availableGridSizes[currentIndex];
            userCurrentGridSize[sessionId] = newSize;
            agent.SendChat($"Grid size changed to: {newSize}");
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"User {agent.AgentInfo.Name} cycled grid size to {newSize}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in CycleGridSize: {ex.Message}");
        }
    }

    // --- Emissive pulse and animation: revert to stubs for Sansar API compatibility ---
    // Method to trigger emissive pulse effect on newly placed blocks
    private void TriggerEmissivePulse(Cluster blockCluster, string gridKey)
    {
        try
        {
            if (blockCluster == null || !blockCluster.IsValid)
                return;

            // Check all objects in the cluster for emissive materials
            var objectPrivates = blockCluster.GetObjectPrivates();
            bool hasEmissiveMaterial = false;

            foreach (var objectPrivate in objectPrivates)
            {
                if (objectPrivate != null && objectPrivate.TryGetFirstComponent(out MeshComponent meshComponent))
                {
                    if (meshComponent.IsScriptable)
                    {
                        var materials = meshComponent.GetRenderMaterials();
                        foreach (var material in materials)
                        {
                            if (material != null && material.HasEmissiveIntensity)
                            {
                                hasEmissiveMaterial = true;
                                // Start the pulse animation: 0 → 100 → 0 over the specified duration
                                StartEmissivePulseAnimation(material, gridKey);
                            }
                        }
                    }
                }
            }

            if (DebugMode && hasEmissiveMaterial)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Started emissive pulse effect for block at {gridKey}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in TriggerEmissivePulse: {ex.Message}");
        }
    }

    // Method to animate emissive intensity with smart intensity rules
    private void StartEmissivePulseAnimation(RenderMaterial material, string gridKey)
    {
        try
        {
            // Get current material properties
            MaterialProperties currentProps = material.GetProperties();
            float originalEmissive = currentProps.EmissiveIntensity;
            // Apply smart intensity rules:
            // - For materials with intensity above 20: add 10 to current intensity
            // - For materials with intensity below 20: pulse to intensity 10
            float targetIntensity;
            if (originalEmissive > 20.0f)
            {
                // High intensity materials: add 10, but cap at 30 to stay within safe range
                targetIntensity = Math.Min(originalEmissive + 10.0f, 30.0f);
            }
            else
            {
                // Low intensity materials: pulse to intensity 10
                targetIntensity = 10.0f;
            }
            // Only animate if there's actually a difference to show
            if (Math.Abs(targetIntensity - originalEmissive) < 0.1f)
            {
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, $"Skipping emissive pulse for block at {gridKey} - target intensity ({targetIntensity}) too close to original ({originalEmissive})");
                }
                return;
            }
            // Phase 1: Animate from current intensity to target intensity over half the duration
            MaterialProperties phase1Props = currentProps;
            phase1Props.EmissiveIntensity = targetIntensity;
            float halfDuration = EmissivePulseDuration / 2.0f;
            // Start phase 1 (current → target)
            material.SetProperties(phase1Props, halfDuration, InterpolationMode.Linear, (data) =>
            {
                // Phase 1 complete, now start phase 2 (target → original)
                if (data.Success)
                {
                    try
                    {
                        MaterialProperties phase2Props = material.GetProperties();
                        phase2Props.EmissiveIntensity = originalEmissive; // Return to original intensity
                        // Start phase 2 (target → original)
                        material.SetProperties(phase2Props, halfDuration, InterpolationMode.Linear);
                        if (DebugMode)
                        {
                            Log.Write(LogLevel.Info, GetType().Name, $"Emissive pulse phase 2 started for block at {gridKey} (returning to {originalEmissive})");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Write(LogLevel.Error, GetType().Name, $"Error in emissive pulse phase 2: {ex.Message}");
                    }
                }
            });
            if (DebugMode)
            {
                string ruleDescription = originalEmissive > 20.0f ? "high intensity (+10)" : "low intensity (→10)";
                Log.Write(LogLevel.Info, GetType().Name, $"Emissive pulse phase 1 started for block at {gridKey} ({originalEmissive} → {targetIntensity} → {originalEmissive}, rule: {ruleDescription}, duration: {halfDuration}s each phase)");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in StartEmissivePulseAnimation: {ex.Message}");
        }
    }

    // --- World cleaning chat command handlers: remove IsAdmin and StartCoroutine usage ---
    private void HandleCleanWorldCommand(ChatData data)
    {
        try
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
            if (agent == null) return;

            // Check if world cleaning is enabled or if user is the world owner
            bool isWorldOwner = false;
            try {
                isWorldOwner = agent.AgentInfo.AvatarUuid == ScenePrivate.SceneInfo.AvatarUuid;
            } catch { }
            if (!worldCleaningEnabled && !isWorldOwner)
            {
                agent.SendChat("World cleaning is currently disabled. Only the world owner can clean the world.");
                return;
            }

            int blockCount = placedBlocks.Count;
            if (blockCount == 0)
            {
                agent.SendChat("No blocks to clean - the world is already empty.");
                return;
            }

            // Notify all users about the cleaning
            ScenePrivate.Chat.MessageAllUsers($"{agent.AgentInfo.Name} is cleaning the world ({blockCount} blocks)...");

            // Start the cleaning process
            StartCoroutine(CleanWorldCoroutine, agent);

            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"World cleaning initiated by {agent.AgentInfo.Name} - {blockCount} blocks to remove");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleCleanWorldCommand: {ex.Message}");
        }
    }

    // Coroutine to clean the world in batches for better performance
    private void CleanWorldCoroutine(AgentPrivate initiatingAgent)
    {
        try
        {
            const int batchSize = 10; // Process 10 blocks at a time
            const float batchDelay = 0.1f; // 100ms delay between batches
            var blocksToRemove = new List<string>(placedBlocks.Keys);
            int totalBlocks = blocksToRemove.Count;
            int processedBlocks = 0;
            // Process blocks in batches
            for (int i = 0; i < blocksToRemove.Count; i += batchSize)
            {
                int currentBatchSize = Math.Min(batchSize, blocksToRemove.Count - i);
                // Remove current batch
                for (int j = 0; j < currentBatchSize; j++)
                {
                    string gridKey = blocksToRemove[i + j];
                    if (placedBlocks.TryGetValue(gridKey, out Cluster blockCluster))
                    {
                        if (blockCluster != null && blockCluster.IsValid)
                        {
                            blockCluster.Destroy();
                        }
                        // Clean up tracking dictionaries
                        placedBlocks.Remove(gridKey);
                        blockRotations.Remove(gridKey);
                        blockTypes.Remove(gridKey);
                        processedBlocks++;
                    }
                }
                // Update progress every few batches
                if ((i / batchSize) % 5 == 0 || i + batchSize >= blocksToRemove.Count)
                {
                    float progress = (float)processedBlocks / totalBlocks * 100f;
                    ScenePrivate.Chat.MessageAllUsers($"Cleaning progress: {processedBlocks}/{totalBlocks} blocks ({progress:F0}%)");
                }
                // Small delay between batches to prevent performance issues
                if (i + batchSize < blocksToRemove.Count)
                {
                    Wait(TimeSpan.FromSeconds(batchDelay));
                }
            }
            // Final notification
            ScenePrivate.Chat.MessageAllUsers($"World cleaning complete! Removed {processedBlocks} blocks.");
            // Play deletion sound for the initiating agent
            PlaySound(DeleteBlockSound, initiatingAgent);
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"World cleaning completed - {processedBlocks} blocks removed");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in CleanWorldCoroutine: {ex.Message}");
            ScenePrivate.Chat.MessageAllUsers("Error occurred during world cleaning. Process may be incomplete.");
        }
    }
    // Helper method to convert grid key back to position
    private Vector GetPositionFromGridKey(string gridKey, float gridSize)
    {
        var parts = gridKey.Split(',');
        if (parts.Length != 3) return Vector.Zero;
        if (float.TryParse(parts[0], out float x) && float.TryParse(parts[1], out float y) && float.TryParse(parts[2], out float z))
        {
            return new Vector(x * gridSize, y * gridSize, z * gridSize);
        }
        return Vector.Zero;
    }

    // --- Additional chat command handlers for world cleaning ---
    private void HandleCleanOffCommand(ChatData data)
    {
        try
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
            if (agent == null) return;
            bool isWorldOwner = false;
            try {
                isWorldOwner = agent.AgentInfo.AvatarUuid == ScenePrivate.SceneInfo.AvatarUuid;
            } catch { }
            if (!isWorldOwner)
            {
                agent.SendChat("Only the world owner can disable world cleaning.");
                return;
            }
            worldCleaningEnabled = false;
            agent.SendChat("World cleaning is now disabled for players. Only you (world owner) can clean the world.");
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"World cleaning disabled by owner {agent.AgentInfo.Name}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleCleanOffCommand: {ex.Message}");
        }
    }

    private void HandleCleanOnCommand(ChatData data)
    {
        try
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
            if (agent == null) return;
            bool isWorldOwner = false;
            try {
                isWorldOwner = agent.AgentInfo.AvatarUuid == ScenePrivate.SceneInfo.AvatarUuid;
            } catch { }
            if (!isWorldOwner)
            {
                agent.SendChat("Only the world owner can enable world cleaning.");
                return;
            }
            worldCleaningEnabled = true;
            agent.SendChat("World cleaning is now enabled for all players.");
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"World cleaning enabled by owner {agent.AgentInfo.Name}");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, GetType().Name, $"Error in HandleCleanOnCommand: {ex.Message}");
        }
    }

    private void HandleBlockTint(AgentPrivate agent, CommandData data)
    {
        // Use the targeting data to find which block was clicked
        if (data.TargetingComponent == null)
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for tinting");
            }
            return;
        }

        // Find the block that was clicked
        string blockToTint = null;
        Cluster targetCluster = null;
        foreach (var kvp in placedBlocks)
        {
            if (kvp.Value != null && kvp.Value.IsValid)
            {
                var objectPrivates = kvp.Value.GetObjectPrivates();
                foreach (var objectPrivate in objectPrivates)
                {
                    if (objectPrivate != null)
                    {
                        if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                        {
                            blockToTint = kvp.Key;
                            targetCluster = kvp.Value;
                            break;
                        }
                    }
                }
                if (blockToTint != null) break;
            }
        }

        if (blockToTint != null && targetCluster != null)
        {
            RandomTintBlock(targetCluster);
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, $"Block at {blockToTint} tinted by {agent.AgentInfo.Name}");
            }
        }
        else
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No block found to tint at clicked position");
            }
        }
    }

    private void RandomTintBlock(Cluster cluster)
    {
        var random = new System.Random();
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            if (objectPrivate != null && objectPrivate.TryGetFirstComponent(out MeshComponent mesh))
            {
                if (mesh.IsScriptable)
                {
                    foreach (var mat in mesh.GetRenderMaterials())
                    {
                        if (mat.HasTint)
                        {
                            var props = mat.GetProperties();
                            props.Tint = new Color(
                                (float)random.NextDouble(),
                                (float)random.NextDouble(),
                                (float)random.NextDouble(),
                                1.0f);
                            mat.SetProperties(props);
                        }
                    }
                }
            }
        }
    }

    private void ToggleTintMode(AgentPrivate agent)
    {
        SessionId sessionId = agent.AgentInfo.SessionId;
        int currentMode = 0;
        userModes.TryGetValue(sessionId, out currentMode);
        if (currentMode == TINT_MODE)
        {
            // Exiting tint mode: switch back to build mode
            userModes[sessionId] = 0;
            agent.SendChat("Exited TINT MODE. Switched to BUILD MODE.");
        }
        else
        {
            // Entering tint mode
            userModes[sessionId] = TINT_MODE;
            agent.SendChat("Switched to TINT MODE - Click a block to randomly tint it");
        }
    }

    // 4. Add the OnReaction handler
    private void OnReaction(ReactionData data)
    {
        if (data == null) return;

        // Find the closest agent to the reaction position
        AgentPrivate agent = null;
        float minDist = float.MaxValue;
        foreach (var a in ScenePrivate.GetAgents())
        {
            ObjectPrivate agentObj = ScenePrivate.FindObject(a.AgentInfo.ObjectId);
            float dist = (agentObj.Position - data.Position).Length();
            if (dist < minDist)
            {
                minDist = dist;
                agent = a;
            }
        }
        if (agent == null || !agent.IsValid) return;
        SessionId sessionId = agent.AgentInfo.SessionId;

        // Map reaction type to action and cluster
        string action = null;
        ThumbnailedClusterResource cluster = null;
        if (data.Type == "Grid.Swap") { action = ReactionAction1; cluster = ReactionCluster1; }
        else if (data.Type == "Grid.Cycle") { action = ReactionAction2; cluster = ReactionCluster2; }
        else if (data.Type == "Grid.Add") { action = ReactionAction3; cluster = ReactionCluster3; }
        else if (data.Type == "Grid.Remove") { action = ReactionAction4; cluster = ReactionCluster4; }
        else if (data.Type == "Grid.Tint") { action = ReactionAction5; cluster = ReactionCluster5; }
        else if (data.Type == "Grid.Copy") { action = ReactionAction6; cluster = ReactionCluster6; }
        else if (data.Type == "Grid.Mode7") { action = ReactionAction7; cluster = ReactionCluster7; }
        else if (data.Type == "Grid.Mode8") { action = ReactionAction8; cluster = ReactionCluster8; }
        else if (data.Type == "Grid.Mode9") { action = ReactionAction9; cluster = ReactionCluster9; }
        else return;

        // Call the same logic as the key handler for each Action key
        if (action == "Action1")
        {
            ToggleSwapMode(agent);
        }
        else if (action == "Action2")
        {
            CycleBlockType(agent);
        }
        else if (action == "Action3")
        {
            ToggleBuildDeleteMode(agent);
        }
        else if (action == "Action4")
        {
            CycleMode(agent);
        }
        else if (action == "Action5")
        {
            ToggleCopyMode(agent);
        }
        else if (action == "Action6")
        {
            ToggleTintMode(agent);
        }
        else if (action == "Action7")
        {
            TogglePasteTintMode(agent);
        }
        else if (action == "Action9")
        {
            if (EnableDynamicGridSizes)
                CycleGridSize(agent);
        }
        else if (action == "Action0")
        {
            ToggleGrid(agent);
        }
        else if (action == "Paste_Tint")
        {
            TogglePasteTintMode(agent);
        }
        // Add any additional custom action logic here

        // Spawn the cluster at agent's position + offset
        if (cluster != null && cluster.IsValid)
        {
            ObjectPrivate agentObj = ScenePrivate.FindObject(agent.AgentInfo.ObjectId);
            Vector pos = agentObj.Position + ReactionOffset;
            Quaternion rot = agentObj.Rotation;
            ScenePrivate.CreateCluster(cluster.ClusterResource, pos, rot, Vector.Zero, (ScenePrivate.CreateClusterData ccd) =>
            {
                if (ccd.ClusterReference != null && ccd.ClusterReference.IsValid)
                {
                    StartCoroutine(() => DelayedDestroy(ccd.ClusterReference, ReactionDuration));
                }
            });
        }
    }

    private void DelayedDestroy(Cluster cluster, float delay)
    {
        Wait(TimeSpan.FromSeconds(delay));
        if (cluster != null && cluster.IsValid)
            cluster.Destroy();
    }

    // Add a new handler for key 7 (Paste_Tint mode)
    private void TogglePasteTintMode(AgentPrivate agent)
    {
        SessionId sessionId = agent.AgentInfo.SessionId;
        userModes[sessionId] = PASTE_TINT_MODE;
        if (userCopiedTints.TryGetValue(sessionId, out var tints) && tints != null && tints.Count > 0)
        {
            userLastCopiedTint[sessionId] = tints[tints.Count - 1];
            agent.SendChat("Paste Tint: Using last copied block's tint.");
        }
        else
        {
            agent.SendChat("Paste Tint: No copied block tint found. Copy a block first.");
        }
        agent.SendChat("Switched to PASTE TINT MODE - Click a block to apply the copied tint");
    }

    // Helper to apply a tint to a block
    private void ApplyTintToBlock(Cluster cluster, Color tint)
    {
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            if (objectPrivate != null && objectPrivate.TryGetFirstComponent(out MeshComponent mesh))
            {
                if (mesh.IsScriptable)
                {
                    foreach (var mat in mesh.GetRenderMaterials())
                    {
                        if (mat.HasTint)
                        {
                            var props = mat.GetProperties();
                            props.Tint = tint;
                            mat.SetProperties(props);
                        }
                    }
                }
            }
        }
    }

    // Add the handler for paste tint mode
    private void HandlePasteTint(AgentPrivate agent, CommandData data)
    {
        // Use the targeting data to find which block was clicked
        if (data.TargetingComponent == null)
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No targeting component found for paste tint");
            }
            return;
        }

        // Find the block that was clicked
        string blockToTint = null;
        Cluster targetCluster = null;
        foreach (var kvp in placedBlocks)
        {
            if (kvp.Value != null && kvp.Value.IsValid)
            {
                var objectPrivates = kvp.Value.GetObjectPrivates();
                foreach (var objectPrivate in objectPrivates)
                {
                    if (objectPrivate != null)
                    {
                        if (CheckObjectForTargetComponent(objectPrivate, data.TargetingComponent))
                        {
                            blockToTint = kvp.Key;
                            targetCluster = kvp.Value;
                            break;
                        }
                    }
                }
                if (blockToTint != null) break;
            }
        }

        if (blockToTint != null && targetCluster != null)
        {
            if (userLastCopiedTint.TryGetValue(agent.AgentInfo.SessionId, out Color tint))
            {
                ApplyTintToBlock(targetCluster, tint);
                // agent.SendChat("Block tinted with copied color.");
                if (DebugMode)
                {
                    Log.Write(LogLevel.Info, GetType().Name, $"Block at {blockToTint} paste-tinted by {agent.AgentInfo.Name}");
                }
            }
            else
            {
                agent.SendChat("No copied tint found. Copy a block first using key 5.");
            }
        }
        else
        {
            if (DebugMode)
            {
                Log.Write(LogLevel.Info, GetType().Name, "No block found to paste-tint at clicked position");
            }
        }
    }

    // Helper: Get the best placement direction from a hit on a block
    private Vector GetPlacementDirectionFromHit(Vector hitPoint, Vector colliderCenter, Vector colliderExtents, Quaternion blockRotation)
    {
        // Transform hit point into local space of the block
        Vector localHit = (hitPoint - colliderCenter).Rotate(blockRotation.Inverse());
        Vector absLocal = new Vector(Math.Abs(localHit.X), Math.Abs(localHit.Y), Math.Abs(localHit.Z));
        Vector ext = colliderExtents;

        // Find the axis with the largest proximity to the extents (face/edge/corner)
        float dx = Math.Abs(absLocal.X - ext.X);
        float dy = Math.Abs(absLocal.Y - ext.Y);
        float dz = Math.Abs(absLocal.Z - ext.Z);
        float minDist = Math.Min(dx, Math.Min(dy, dz));

        // Snap to face normal if close to face, else use direction from center
        if (minDist < ext.X * 0.2f) // Near a face (20% of half-size)
        {
            if (dx == minDist)
                return new Vector(Math.Sign(localHit.X), 0, 0).Rotate(blockRotation);
            if (dy == minDist)
                return new Vector(0, Math.Sign(localHit.Y), 0).Rotate(blockRotation);
            if (dz == minDist)
                return new Vector(0, 0, Math.Sign(localHit.Z)).Rotate(blockRotation);
        }
        // Edge/corner: use direction from center, snapped to axis
        Vector dir = localHit.Normalized();
        // Snap to closest axis
        if (Math.Abs(dir.X) > Math.Abs(dir.Y) && Math.Abs(dir.X) > Math.Abs(dir.Z))
            return new Vector(Math.Sign(dir.X), 0, 0).Rotate(blockRotation);
        if (Math.Abs(dir.Y) > Math.Abs(dir.X) && Math.Abs(dir.Y) > Math.Abs(dir.Z))
            return new Vector(0, Math.Sign(dir.Y), 0).Rotate(blockRotation);
        return new Vector(0, 0, Math.Sign(dir.Z)).Rotate(blockRotation);
    }

    // Helper: Snap a position to the grid (now takes gridSize as parameter)
    private Vector SnapToGrid(Vector pos, float gridSize)
    {
        float gridX = (float)Math.Round(pos.X / gridSize) * gridSize;
        float gridY = (float)Math.Round(pos.Y / gridSize) * gridSize;
        float gridZ = (float)Math.Round(pos.Z / gridSize) * gridSize;
        return new Vector(gridX, gridY, gridZ);
    }

    // Add this helper method inside the class (anywhere appropriate):
    private bool IsVectorEqual(Vector a, Vector b, float epsilon = 0.0001f)
    {
        return Math.Abs(a.X - b.X) < epsilon &&
               Math.Abs(a.Y - b.Y) < epsilon &&
               Math.Abs(a.Z - b.Z) < epsilon;
    }

}