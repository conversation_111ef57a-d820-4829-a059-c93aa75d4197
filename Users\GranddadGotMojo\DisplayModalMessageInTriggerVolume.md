# Display Modal Message From Trigger Volume in Chat

This script is meant to be put in a Collision Volume.  The Modal Volume displays a user interface with two buttons.  You select either button and a message is displayed in chat.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/ModalMessage.png)

**Modal Message** - the message associated with the left button.

**Help Message** - the message associated with the right button.

In this example, there are two buttons.  An OK button and a help button.  When the user enters the collision volume, the dialog is displayed with the two buttons.  

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/ModalMessageInWorld1.png)

If the user clicks on the Help Button the following message is displayed in their chat window.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/ModalMessageInWorld2.png)
