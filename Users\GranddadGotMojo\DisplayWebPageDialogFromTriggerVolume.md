# Display Web Page Dialog from Trigger Volume

This script is meant to be put in a Collision Volume.  The Modal Volume displays a http url in the chat window for people to click on to launch a web browser with that url.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/DisplayWebPage.png)

**Web Page** - the URL to display in chat.

In this example, when the person enters the trigger volume the url in the script is sent to the chat window for the user to click on.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/DisplayWebPage2.png)

When the user clicks on the url in the chat window it brings up the web page in their browser.  In this case, it went to a page in the store so you could get my free tee shirt.

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/DisplayWebPage3.png)
