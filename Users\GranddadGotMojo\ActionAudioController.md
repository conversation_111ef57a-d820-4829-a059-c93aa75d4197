# Action Audio Controller

The Action Audio Controller Script is a script that allows you to control volume playing in the experience through Cha<PERSON> of with other Triggers you build yourself.  

![](https://github.com/mojoD/San<PERSON>-Simple-And-Reflex-Script-Integration/blob/master/images/ActionAudioController.png)

**Initial Volume** - the initial volume of the audio source.

This script works through chat.  The chat commands are:

**/VolumeUp** - turns up current volume by 5 decibels.  

**/VolumeDown** - turns down current volume by 5 decibels.

**/VolumeOn** - turns audio volume on if it is off.

**/VolumeOff** - turns audio volume off if it is on.

