# Sansar Script Patterns and Templates

This document provides common patterns and templates for Sansar scripting, organized by complexity and use case.

## Basic Script Templates

### 1. Hello World Interaction Script

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class HelloWorld : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Click Me!")]
    public Interaction MyInteraction;
    #endregion

    public override void Init()
    {
        MyInteraction.Subscribe(OnClick);
    }

    private void OnClick(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent != null && agent.IsValid)
        {
            agent.SendChat("Hello! Thanks for clicking me.");
        }
    }
}
```

### 2. Basic Event Listener

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class EventListener : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("myevent")]
    public string EventName = "myevent";
    
    [DefaultValue(true)]
    public bool DebugLogging = true;
    #endregion

    public override void Init()
    {
        SubscribeToScriptEvent(EventName, OnScriptEvent);
        if (DebugLogging) Log.Write($"Listening for event: {EventName}");
    }

    private void OnScriptEvent(ScriptEventData data)
    {
        if (DebugLogging) Log.Write($"Received event: {EventName}");
        // Handle event data
    }
}
```

### 3. Basic Chat Command Handler

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class ChatCommandHandler : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("/mycommand")]
    public string CommandPrefix = "/mycommand";
    
    [DefaultValue(false)]
    public bool OwnerOnly = false;
    #endregion

    public override void Init()
    {
        ScenePrivate.Chat.Subscribe(0, null, OnChat);
    }

    private void OnChat(ChatData data)
    {
        if (data.Message.StartsWith(CommandPrefix))
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
            if (agent == null || !agent.IsValid) return;

            if (OwnerOnly && !IsSceneOwner(agent))
            {
                agent.SendChat("You must be the scene owner to use this command.");
                return;
            }

            // Handle command
            HandleCommand(agent, data.Message);
        }
    }

    private void HandleCommand(AgentPrivate agent, string message)
    {
        agent.SendChat("Command received!");
    }

    private bool IsSceneOwner(AgentPrivate agent)
    {
        return agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
    }
}
```

## Animation Patterns

### 4. Simple Animation Controller

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class AnimationController : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Trigger Animation")]
    public Interaction TriggerInteraction;
    
    [DefaultValue("MyAnimation")]
    public string AnimationName = "MyAnimation";
    #endregion

    private AnimationComponent animationComponent;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            TriggerInteraction.Subscribe(OnTrigger);
        }
        else
        {
            Log.Write("No animation component found!");
        }
    }

    private void OnTrigger(InteractionData data)
    {
        var animation = animationComponent.GetAnimation(AnimationName);
        if (animation != null)
        {
            animation.Play();
        }
    }
}
```

### 5. Animation with Events

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class AnimationWithEvents : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Play Animation")]
    public Interaction PlayInteraction;
    
    [DefaultValue("MyAnimation")]
    public string AnimationName = "MyAnimation";
    
    [DefaultValue("animation_complete")]
    public string CompletionEvent = "animation_complete";
    #endregion

    private AnimationComponent animationComponent;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out animationComponent))
        {
            PlayInteraction.Subscribe(OnPlay);
            animationComponent.Subscribe(AnimationName, OnAnimationEvent);
        }
    }

    private void OnPlay(InteractionData data)
    {
        var animation = animationComponent.GetAnimation(AnimationName);
        if (animation != null)
        {
            animation.Play();
        }
    }

    private void OnAnimationEvent(AnimationData data)
    {
        if (data.Phase == AnimationEventPhase.Done)
        {
            PostScriptEvent(CompletionEvent, new SimpleScriptEventData("animation_done"));
        }
    }
}
```

## Physics and Collision Patterns

### 6. Trigger Volume Handler

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class TriggerVolumeHandler : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Welcome to the zone!")]
    public string EnterMessage = "Welcome to the zone!";
    
    [DefaultValue("Thanks for visiting!")]
    public string ExitMessage = "Thanks for visiting!";
    #endregion

    private RigidBodyComponent rigidBody;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out rigidBody))
        {
            if (rigidBody.IsTriggerVolume())
            {
                rigidBody.Subscribe(CollisionEventType.Trigger, OnTrigger);
            }
            else
            {
                Log.Write("RigidBody is not set as a trigger volume!");
            }
        }
        else
        {
            Log.Write("No RigidBody component found!");
        }
    }

    private void OnTrigger(CollisionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.HitComponentId.ObjectId);
        if (agent != null && agent.IsValid)
        {
            if (data.Phase == CollisionEventPhase.TriggerEnter)
            {
                agent.SendChat(EnterMessage);
            }
            else if (data.Phase == CollisionEventPhase.TriggerExit)
            {
                agent.SendChat(ExitMessage);
            }
        }
    }
}
```

### 7. Physics Impact Handler

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class ImpactHandler : SceneObjectScript
{
    #region EditorProperties
    [Range(0.1, 100.0)]
    [DefaultValue(5.0)]
    public double MinimumImpactForce = 5.0;
    
    [DefaultValue("impact_event")]
    public string ImpactEventName = "impact_event";
    #endregion

    private RigidBodyComponent rigidBody;

    public override void Init()
    {
        if (ObjectPrivate.TryGetFirstComponent(out rigidBody))
        {
            rigidBody.Subscribe(CollisionEventType.CharacterContact, OnCollision);
            rigidBody.Subscribe(CollisionEventType.RigidBodyContact, OnCollision);
        }
    }

    private void OnCollision(CollisionData data)
    {
        if (data.Phase == CollisionEventPhase.Contact)
        {
            double impactForce = data.Impulse.Length();
            if (impactForce >= MinimumImpactForce)
            {
                PostScriptEvent(ImpactEventName, 
                    new SimpleScriptEventData($"Impact force: {impactForce:F2}"));
            }
        }
    }
}
```

## Audio Patterns

### 8. Positional Audio Player

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class PositionalAudioPlayer : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Play Sound")]
    public Interaction PlayInteraction;
    
    public SoundResource AudioClip;
    
    [Range(0.0, 60.0)]
    [DefaultValue(10.0)]
    public double Volume = 10.0;
    
    [DefaultValue(false)]
    public bool PlayForAllUsers = false;
    #endregion

    public override void Init()
    {
        if (AudioClip != null)
        {
            PlayInteraction.Subscribe(OnPlay);
        }
        else
        {
            Log.Write("No audio clip assigned!");
        }
    }

    private void OnPlay(InteractionData data)
    {
        PlaySettings settings = PlaySettings.PlayOnce;
        settings.Loudness = (float)Volume;
        
        if (PlayForAllUsers)
        {
            ScenePrivate.PlaySound(AudioClip, settings);
        }
        else
        {
            AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
            if (agent != null && agent.IsValid)
            {
                agent.PlaySound(AudioClip, settings);
            }
        }
    }
}
```

## Coroutine Patterns

### 9. Periodic Update Loop

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class PeriodicUpdater : SceneObjectScript
{
    #region EditorProperties
    [Range(0.1, 60.0)]
    [DefaultValue(1.0)]
    public double UpdateInterval = 1.0;
    
    [DefaultValue(true)]
    public bool AutoStart = true;
    
    [DefaultValue("Start Updates")]
    public Interaction StartInteraction;
    
    [DefaultValue("Stop Updates")]
    public Interaction StopInteraction;
    #endregion

    private bool isUpdating = false;

    public override void Init()
    {
        StartInteraction.Subscribe(OnStart);
        StopInteraction.Subscribe(OnStop);
        
        if (AutoStart)
        {
            StartUpdates();
        }
    }

    private void OnStart(InteractionData data)
    {
        StartUpdates();
    }

    private void OnStop(InteractionData data)
    {
        StopUpdates();
    }

    private void StartUpdates()
    {
        if (!isUpdating)
        {
            isUpdating = true;
            StartCoroutine(UpdateLoop);
        }
    }

    private void StopUpdates()
    {
        isUpdating = false;
    }

    private void UpdateLoop()
    {
        while (isUpdating)
        {
            // Perform periodic update
            DoUpdate();
            
            Wait(UpdateInterval);
        }
    }

    private void DoUpdate()
    {
        // Your update logic here
        Log.Write($"Update at {DateTime.Now}");
    }
}
```

### 10. User Tracking Coroutine

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Diagnostics;

public class UserTracker : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue(true)]
    public bool AnnounceJoinLeave = true;
    
    [DefaultValue(true)]
    public bool TrackVisitDuration = true;
    #endregion

    public override void Init()
    {
        StartCoroutine(TrackNewUsers);
    }

    private void TrackNewUsers()
    {
        while (true)
        {
            UserData data = (UserData)WaitFor(ScenePrivate.User.Subscribe, User.AddUser, SessionId.Invalid);
            
            if (AnnounceJoinLeave)
            {
                AgentPrivate agent = ScenePrivate.FindAgent(data.User);
                if (agent != null && agent.IsValid)
                {
                    ScenePrivate.Chat.MessageAllUsers($"{agent.AgentInfo.Name} has joined the scene.");
                }
            }
            
            if (TrackVisitDuration)
            {
                StartCoroutine(TrackUser, data.User);
            }
        }
    }

    private void TrackUser(SessionId userId)
    {
        long joined = Stopwatch.GetTimestamp();
        
        AgentPrivate agent = ScenePrivate.FindAgent(userId);
        string name = agent?.AgentInfo.Name ?? "Unknown";
        
        WaitFor(ScenePrivate.User.Subscribe, User.RemoveUser, userId);
        
        if (AnnounceJoinLeave)
        {
            TimeSpan elapsed = TimeSpan.FromTicks(Stopwatch.GetTimestamp() - joined);
            ScenePrivate.Chat.MessageAllUsers(
                $"{name} was present for {elapsed.TotalSeconds:F1} seconds");
        }
    }
}
```

## Data Storage Patterns

### 11. Simple Data Store Handler

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class DataStoreHandler : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Save Data")]
    public Interaction SaveInteraction;
    
    [DefaultValue("Load Data")]
    public Interaction LoadInteraction;
    
    [DefaultValue("my_data_key")]
    public string DataKey = "my_data_key";
    #endregion

    public override void Init()
    {
        SaveInteraction.Subscribe(OnSave);
        LoadInteraction.Subscribe(OnLoad);
    }

    private void OnSave(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        string dataToSave = $"Saved by {agent.AgentInfo.Name} at {DateTime.UtcNow}";
        
        DataStore.SetValue(DataKey, dataToSave).Subscribe(result => {
            if (result.Success)
            {
                agent.SendChat("Data saved successfully!");
            }
            else
            {
                agent.SendChat("Failed to save data.");
                Log.Write(LogLevel.Error, "DataStore", result.Message);
            }
        });
    }

    private void OnLoad(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        DataStore.GetValue(DataKey).Subscribe(result => {
            if (result.Success)
            {
                agent.SendChat($"Loaded data: {result.Value}");
            }
            else
            {
                agent.SendChat("No data found or failed to load.");
            }
        });
    }
}
```

## Advanced Patterns

### 12. State Machine Pattern

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public enum ScriptState
{
    Idle,
    Active,
    Cooldown
}

public class StateMachine : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Activate")]
    public Interaction ActivateInteraction;
    
    [Range(1.0, 60.0)]
    [DefaultValue(5.0)]
    public double ActiveDuration = 5.0;
    
    [Range(1.0, 30.0)]
    [DefaultValue(3.0)]
    public double CooldownDuration = 3.0;
    #endregion

    private ScriptState currentState = ScriptState.Idle;

    public override void Init()
    {
        ActivateInteraction.Subscribe(OnActivate);
        UpdateInteractionPrompt();
    }

    private void OnActivate(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        switch (currentState)
        {
            case ScriptState.Idle:
                StartActive(agent);
                break;
            case ScriptState.Active:
                agent.SendChat("Already active, please wait...");
                break;
            case ScriptState.Cooldown:
                agent.SendChat("In cooldown, please wait...");
                break;
        }
    }

    private void StartActive(AgentPrivate agent)
    {
        currentState = ScriptState.Active;
        UpdateInteractionPrompt();
        
        agent.SendChat("Activated!");
        
        StartCoroutine(ActiveTimer);
    }

    private void ActiveTimer()
    {
        Wait(ActiveDuration);
        
        currentState = ScriptState.Cooldown;
        UpdateInteractionPrompt();
        
        StartCoroutine(CooldownTimer);
    }

    private void CooldownTimer()
    {
        Wait(CooldownDuration);
        
        currentState = ScriptState.Idle;
        UpdateInteractionPrompt();
    }

    private void UpdateInteractionPrompt()
    {
        switch (currentState)
        {
            case ScriptState.Idle:
                ActivateInteraction.SetPrompt("Activate");
                break;
            case ScriptState.Active:
                ActivateInteraction.SetPrompt("Active...");
                break;
            case ScriptState.Cooldown:
                ActivateInteraction.SetPrompt("Cooldown...");
                break;
        }
    }
}
```

### 13. Multi-Component Controller

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;

public class MultiComponentController : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Control Components")]
    public Interaction ControlInteraction;
    
    [DefaultValue(true)]
    public bool ControlAnimations = true;
    
    [DefaultValue(true)]
    public bool ControlLights = true;
    
    [DefaultValue(true)]
    public bool ControlAudio = true;
    #endregion

    private List<AnimationComponent> animationComponents = new List<AnimationComponent>();
    private List<LightComponent> lightComponents = new List<LightComponent>();
    private List<AudioComponent> audioComponents = new List<AudioComponent>();

    public override void Init()
    {
        CacheComponents();
        ControlInteraction.Subscribe(OnControl);
    }

    private void CacheComponents()
    {
        if (ControlAnimations)
        {
            animationComponents.AddRange(ObjectPrivate.GetComponents<AnimationComponent>());
        }
        
        if (ControlLights)
        {
            lightComponents.AddRange(ObjectPrivate.GetComponents<LightComponent>());
        }
        
        if (ControlAudio)
        {
            audioComponents.AddRange(ObjectPrivate.GetComponents<AudioComponent>());
        }
        
        Log.Write($"Found {animationComponents.Count} animations, " +
                 $"{lightComponents.Count} lights, " +
                 $"{audioComponents.Count} audio components");
    }

    private void OnControl(InteractionData data)
    {
        ControlAllComponents();
    }

    private void ControlAllComponents()
    {
        // Control animations
        foreach (var anim in animationComponents)
        {
            var defaultAnim = anim.DefaultAnimation;
            if (defaultAnim != null)
            {
                defaultAnim.Play();
            }
        }

        // Control lights
        foreach (var light in lightComponents)
        {
            light.SetColor(Sansar.Color.Random());
            light.SetIntensity(1.0f);
        }

        // Control audio
        foreach (var audio in audioComponents)
        {
            var defaultSound = audio.DefaultSound;
            if (defaultSound != null)
            {
                defaultSound.Play();
            }
        }
    }
}
```

### 14. HTTP Client Pattern

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class HttpClientExample : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Fetch Data")]
    public Interaction FetchInteraction;
    
    [DefaultValue("https://api.example.com/data")]
    public string ApiUrl = "https://api.example.com/data";
    
    [DefaultValue(10)]
    public int TimeoutSeconds = 10;
    #endregion

    public override void Init()
    {
        FetchInteraction.Subscribe(OnFetch);
    }

    private void OnFetch(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        agent.SendChat("Fetching data...");
        StartCoroutine(FetchData, agent);
    }

    private void FetchData(AgentPrivate agent)
    {
        try
        {
            HttpClient client = ScenePrivate.CreateHttpClient();
            HttpRequestOptions options = new HttpRequestOptions();
            options.Method = HttpRequestMethod.GET;
            options.Timeout = TimeSpan.FromSeconds(TimeoutSeconds);

            HttpRequestData response = WaitFor(client.Request, ApiUrl, options) as HttpRequestData;

            if (response.Success)
            {
                agent.SendChat($"Data received: {response.Body.Substring(0, Math.Min(100, response.Body.Length))}...");
            }
            else
            {
                agent.SendChat($"Request failed: {response.Message}");
                Log.Write(LogLevel.Warning, "HTTP", $"Request failed: {response.Message}");
            }
        }
        catch (Exception e)
        {
            agent.SendChat("Error occurred during request.");
            Log.Write(LogLevel.Error, "HTTP", $"Exception: {e.Message}");
        }
    }
}
```

### 15. ScriptLibrary Integration Pattern

```csharp
using Sansar.Script;
using Sansar.Simulation;
using ScriptLibrary;
using System;

public class LibraryIntegrationExample : LibraryBase
{
    #region EditorProperties
    [DefaultValue("on")]
    public string OnEvent = "on";
    
    [DefaultValue("off")]
    public string OffEvent = "off";
    
    [DefaultValue("status_changed")]
    public string StatusEvent = "status_changed";
    #endregion

    private bool isEnabled = false;

    protected override void SimpleInit()
    {
        SubscribeToAll(OnEvent, OnEnable);
        SubscribeToAll(OffEvent, OnDisable);
    }

    private void OnEnable(ScriptEventData data)
    {
        if (!isEnabled)
        {
            isEnabled = true;
            SimpleLog(LogLevel.Info, "Component enabled");
            
            SendToAll(StatusEvent, new SimpleData(this) { 
                AgentInfo = data.Data?.AsInterface<ISimpleData>()?.AgentInfo,
                ObjectId = ObjectPrivate.ObjectId,
                SourceObjectId = data.Data?.AsInterface<ISimpleData>()?.SourceObjectId ?? ObjectId.Invalid
            });
        }
    }

    private void OnDisable(ScriptEventData data)
    {
        if (isEnabled)
        {
            isEnabled = false;
            SimpleLog(LogLevel.Info, "Component disabled");
            
            SendToAll(StatusEvent, new SimpleData(this) { 
                AgentInfo = data.Data?.AsInterface<ISimpleData>()?.AgentInfo,
                ObjectId = ObjectPrivate.ObjectId,
                SourceObjectId = data.Data?.AsInterface<ISimpleData>()?.SourceObjectId ?? ObjectId.Invalid
            });
        }
    }
}
```

### 16. Keyboard Input Handler

```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;

public class KeyboardInputHandler : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue(true)]
    public bool EnableKeyboardInput = true;
    
    [DefaultValue(true)]
    public bool LogKeyPresses = false;
    
    [DefaultValue("keyboard_input")]
    public string KeyboardEventName = "keyboard_input";
    #endregion

    private Dictionary<string, string> keyMappings = new Dictionary<string, string>();

    public override void Init()
    {
        if (EnableKeyboardInput)
        {
            SetupKeyMappings();
            ScenePrivate.User.Subscribe(User.AddUser, OnUserJoined);
            ScenePrivate.User.Subscribe(User.RemoveUser, OnUserLeft);
        }
    }

    private void SetupKeyMappings()
    {
        // Map commands to friendly names
        keyMappings = new Dictionary<string, string>
        {
            { "PrimaryAction", "F" },
            { "SecondaryAction", "R" },
            { "Action1", "1" },
            { "Action2", "2" },
            { "Action3", "3" },
            { "Action4", "4" },
            { "Action5", "5" },
            { "Confirm", "Enter" },
            { "Cancel", "Escape" },
            { "SelectUp", "Up Arrow" },
            { "SelectDown", "Down Arrow" },
            { "SelectLeft", "Left Arrow" },
            { "SelectRight", "Right Arrow" }
        };
    }

    private void OnUserJoined(UserData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.User);
        if (agent != null && agent.IsValid)
        {
            // Subscribe to all mapped commands
            foreach (var mapping in keyMappings)
            {
                agent.Client.SubscribeToCommand(mapping.Key, CommandAction.Pressed, OnKeyPressed);
                agent.Client.SubscribeToCommand(mapping.Key, CommandAction.Released, OnKeyReleased);
            }
            
            if (LogKeyPresses)
            {
                Log.Write($"Keyboard input enabled for {agent.AgentInfo.Name}");
            }
        }
    }

    private void OnUserLeft(UserData data)
    {
        if (LogKeyPresses)
        {
            Log.Write($"User left, keyboard tracking ended for session {data.User}");
        }
    }

    private void OnKeyPressed(CommandData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        string command = data.Command;
        string keyName = keyMappings.ContainsKey(command) ? keyMappings[command] : command;
        
        if (LogKeyPresses)
        {
            Log.Write($"{agent.AgentInfo.Name} pressed {keyName} ({command})");
        }

        // Handle specific key actions
        switch (command)
        {
            case "PrimaryAction":
                HandlePrimaryAction(agent);
                break;
            case "SecondaryAction":
                HandleSecondaryAction(agent);
                break;
            case "Action1":
            case "Action2":
            case "Action3":
            case "Action4":
            case "Action5":
                HandleNumberAction(agent, command);
                break;
            case "Confirm":
                HandleConfirm(agent);
                break;
            case "Cancel":
                HandleCancel(agent);
                break;
            case "SelectUp":
            case "SelectDown":
            case "SelectLeft":
            case "SelectRight":
                HandleNavigation(agent, command);
                break;
        }

        // Send keyboard event to other scripts
        var eventData = new SimpleScriptEventData($"Key:{command}|User:{agent.AgentInfo.Name}|Action:Pressed");
        PostScriptEvent(KeyboardEventName, eventData);
    }

    private void OnKeyReleased(CommandData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent == null || !agent.IsValid) return;

        string command = data.Command;
        
        // Send release event
        var eventData = new SimpleScriptEventData($"Key:{command}|User:{agent.AgentInfo.Name}|Action:Released");
        PostScriptEvent(KeyboardEventName, eventData);
    }

    private void HandlePrimaryAction(AgentPrivate agent)
    {
        agent.SendChat("Primary action (F) activated!");
    }

    private void HandleSecondaryAction(AgentPrivate agent)
    {
        agent.SendChat("Secondary action (R) activated!");
    }

    private void HandleNumberAction(AgentPrivate agent, string command)
    {
        string number = command.Replace("Action", "");
        agent.SendChat($"Number {number} pressed!");
    }

    private void HandleConfirm(AgentPrivate agent)
    {
        agent.SendChat("Confirmed!");
    }

    private void HandleCancel(AgentPrivate agent)
    {
        agent.SendChat("Cancelled!");
    }

    private void HandleNavigation(AgentPrivate agent, string direction)
    {
        string directionName = direction.Replace("Select", "").ToLower();
        agent.SendChat($"Navigation: {directionName}");
    }
}
```

## Pattern Selection Guide

### When to Use Each Pattern:

- **Basic Templates (1-3)**: Starting points for simple functionality
- **Animation Patterns (4-5)**: Object movement and visual effects
- **Physics Patterns (6-7)**: Collision detection and physical interactions
- **Audio Patterns (8)**: Sound effects and ambient audio
- **Coroutine Patterns (9-10)**: Time-based operations and async processing
- **Data Patterns (11)**: Persistent storage and retrieval
- **Advanced Patterns (12-15)**: Complex behaviors and system integration
- **Keyboard Input Pattern (16)**: User keyboard interaction handling

### Best Practices:

1. **Start Simple**: Begin with basic templates and add complexity as needed
2. **Error Handling**: Always include null checks and exception handling
3. **Performance**: Use coroutines for time-based operations, avoid busy loops
4. **Modularity**: Break complex functionality into smaller, reusable methods
5. **Documentation**: Use clear variable names and add comments for complex logic