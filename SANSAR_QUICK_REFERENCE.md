# Sansar Quick Reference and Troubleshooting Guide

## Quick API Reference

### Essential Namespaces
```csharp
using Sansar.Script;      // Core scripting framework
using Sansar.Simulation;  // World interaction and components
using System;             // Standard C# features
using System.Collections.Generic; // Collections and data structures
```

### Base Classes
| Class | Purpose | Usage |
|-------|---------|--------|
| `SceneObjectScript` | Scripts attached to objects | Most common base class |
| `LibraryBase` | ScriptLibrary integration | For modular, event-driven scripts |

### Core Properties and Methods

#### ScriptBase
```csharp
// Essential properties
Script.ID                    // Unique script identifier
ObjectPrivate.ObjectId       // Object this script is attached to
ObjectPrivate.Name           // Object name from editor
Log                         // Logging interface
Memory.Usage                // Current memory usage
PendingEventCount           // Queued events count

// Essential methods
Init()                      // Override for initialization
StartCoroutine(method)      // Start async operation
WaitFor(subscription)       // Block until event occurs
PostScriptEvent(name, data) // Send cross-script event
SubscribeToScriptEvent()    // Listen for script events
```

#### Component Access
```csharp
// Get first component of type
AnimationComponent anim;
if (ObjectPrivate.TryGetFirstComponent(out anim)) { }

// Get all components
var comps = ObjectPrivate.GetComponents<ComponentType>();

// Get component by index
var comp = ObjectPrivate.GetComponent(ComponentType.Animation, 0);
```

#### Agent Management
```csharp
// Find agent by ID
AgentPrivate agent = ScenePrivate.FindAgent(sessionId);

// Check agent validity
if (agent != null && agent.IsValid) { }

// Scene owner check
bool isOwner = agent.AgentInfo.Handle.ToLower() == 
               ScenePrivate.SceneInfo.AvatarId.ToLower();
```

## Common Code Snippets

### Basic Script Template
```csharp
using Sansar.Script;
using Sansar.Simulation;
using System;

public class MyScript : SceneObjectScript
{
    #region EditorProperties
    [DefaultValue("Click Me")]
    public Interaction MyInteraction;
    
    [DefaultValue(true)]
    public bool EnableLogging = true;
    #endregion

    public override void Init()
    {
        MyInteraction.Subscribe(OnClick);
        if (EnableLogging) Log.Write("Script initialized");
    }

    private void OnClick(InteractionData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
        if (agent != null && agent.IsValid)
        {
            agent.SendChat("Hello!");
        }
    }
}
```

### Error Handling Template
```csharp
try
{
    // Sansar API calls here
}
catch (Exception e)
{
    Log.Write(LogLevel.Error, "ScriptName", $"Error: {e.Message}");
    // Optional: notify user
    agent?.SendChat("An error occurred");
}
```

### Coroutine Template
```csharp
private void MyCoroutine()
{
    while (condition)
    {
        try
        {
            // Work here
            Wait(interval); // Always include wait
        }
        catch (Exception e)
        {
            Log.Write(LogLevel.Error, "Coroutine", e.Message);
            break; // Exit on error
        }
    }
}
```

### Event Subscription Template
```csharp
// Component events
component.Subscribe(eventName, OnEventHandler);

// Script events
SubscribeToScriptEvent("eventName", OnScriptEvent);

// Scene events
ScenePrivate.User.Subscribe(User.AddUser, OnUserJoin);
ScenePrivate.Chat.Subscribe(0, null, OnChat);
```

## Editor Attributes Reference

### Property Attributes
```csharp
[DefaultValue("default")]           // Set default value
[Range(0.0, 100.0)]                // Numeric range
[Tooltip("Help text")]             // Help tooltip
[DisplayName("Custom Name")]        // Custom display name
```

### Common Property Types
```csharp
public string MyString;             // Text input
public bool MyFlag;                 // Checkbox
public int MyInteger;               // Integer input
public double MyDouble;             // Decimal input
public Vector MyVector;             // 3D vector
public Interaction MyInteraction;   // Makes object clickable
public SoundResource MySound;       // Audio file
public MediaResource MyMedia;       // Media file
```

## Data Types Reference

### Vectors
```csharp
Vector zero = Vector.Zero;                    // (0,0,0)
Vector up = Vector.Up;                        // (0,1,0)
Vector pos = new Vector(1, 2, 3);           // Custom vector
Vector lerped = Vector.Lerp(start, end, t);  // Interpolation
float distance = start.Distance(end);        // Distance
```

### Colors
```csharp
Sansar.Color red = Sansar.Color.Red;
Sansar.Color custom = new Sansar.Color(1.0f, 0.5f, 0.0f); // RGB
Sansar.Color random = Sansar.Color.Random();
```

### Sessions and IDs
```csharp
SessionId userId = agent.AgentInfo.SessionId;
ObjectId objId = ObjectPrivate.ObjectId;
ComponentId compId = component.ComponentId;
```

## Event Data Types

### Interaction Events
```csharp
private void OnClick(InteractionData data)
{
    SessionId userId = data.AgentId;    // Who clicked
    ObjectId objectId = data.ObjectId;  // What was clicked
}
```

### Collision Events
```csharp
private void OnCollision(CollisionData data)
{
    CollisionEventPhase phase = data.Phase;     // Enter/Exit/Contact
    Vector impulse = data.Impulse;              // Collision force
    ComponentId hitId = data.HitComponentId;    // What was hit
}
```

### Animation Events
```csharp
private void OnAnimation(AnimationData data)
{
    string behaviorName = data.BehaviorName;           // Animation name
    AnimationEventPhase phase = data.Phase;           // Start/Done/etc
    ComponentId componentId = data.ComponentId;       // Animation component
}
```

### User Events
```csharp
private void OnUser(UserData data)
{
    SessionId userId = data.User;       // User session ID
    // Use with User.AddUser or User.RemoveUser
}
```

### Chat Events
```csharp
private void OnChat(ChatData data)
{
    string message = data.Message;      // Chat message
    SessionId sourceId = data.SourceId; // Who sent it
}
```

### Keyboard Command Events
```csharp
private void OnKeyPressed(CommandData data)
{
    string command = data.Command;              // Command name (e.g., "PrimaryAction")
    SessionId agentId = data.AgentId;           // Which user pressed the key
    ControlPointType controlPoint = data.ControlPointType; // Input device type
}

// Available commands:
// Trigger, PrimaryAction, SecondaryAction, Modifier
// Action1-Action0 (number keys 1-0)
// Confirm, Cancel, SelectLeft, SelectRight, SelectUp, SelectDown
// Keypad0-Keypad9, KeypadEnter

// Subscribe to keyboard input
agent.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, OnKeyPressed);
```

## Keyboard Commands Reference

### Command to Key Mapping
```csharp
Trigger         // Left Mouse Button
PrimaryAction   // F key
SecondaryAction // R key
Modifier        // Shift key
Action1-Action0 // Number keys 1-0
Confirm         // Enter key
Cancel          // Escape key
SelectLeft      // Left arrow
SelectRight     // Right arrow
SelectUp        // Up arrow
SelectDown      // Down arrow
Keypad0-Keypad9 // Numberpad 0-9
KeypadEnter     // Numberpad Enter
```

### Keyboard Input Setup Pattern
```csharp
public override void Init()
{
    ScenePrivate.User.Subscribe(User.AddUser, OnUserJoined);
}

private void OnUserJoined(UserData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.User);
    if (agent != null && agent.IsValid)
    {
        agent.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, OnKeyPressed);
        agent.Client.SubscribeToCommand("Action1", CommandAction.Pressed, OnKeyPressed);
    }
}
```

## Common Patterns Quick Reference

### Scene Owner Check
```csharp
private bool IsSceneOwner(AgentPrivate agent)
{
    return agent.AgentInfo.Handle.ToLower() == 
           ScenePrivate.SceneInfo.AvatarId.ToLower();
}
```

### Component Validation
```csharp
private bool ValidateComponent<T>(out T component) where T : class
{
    component = null;
    if (!ObjectPrivate.TryGetFirstComponent(out component))
    {
        Log.Write($"No {typeof(T).Name} found on object");
        return false;
    }
    return true;
}
```

### Agent Validation
```csharp
private bool ValidateAgent(SessionId agentId, out AgentPrivate agent)
{
    agent = ScenePrivate.FindAgent(agentId);
    return agent != null && agent.IsValid;
}
```

### Safe Event Posting
```csharp
private void SafePostEvent(string eventName, Reflective data)
{
    try
    {
        PostScriptEvent(eventName, data);
    }
    catch (Exception e)
    {
        Log.Write(LogLevel.Warning, "Event", $"Failed to post {eventName}: {e.Message}");
    }
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. NullReferenceException
**Symptoms**: Script crashes with null reference error
**Causes**: 
- Agent left scene before interaction completed
- Component not found on object
- Trying to access invalid objects

**Solutions**:
```csharp
// Always check agent validity
AgentPrivate agent = ScenePrivate.FindAgent(agentId);
if (agent == null || !agent.IsValid) return;

// Always check component existence
if (!ObjectPrivate.TryGetFirstComponent(out component))
{
    Log.Write("Component not found");
    return;
}

// Check for null before using
if (someObject?.Property != null)
{
    // Safe to use
}
```

#### 2. Memory Limit Exceeded
**Symptoms**: Script stops working, memory warnings in logs
**Causes**:
- Too many coroutines running
- Event subscriptions not cleaned up
- Large data structures in memory

**Solutions**:
```csharp
// Monitor memory usage
if (Memory.Usage > Memory.Limit * 0.8)
{
    Log.Write(LogLevel.Warning, "Memory", "Approaching memory limit");
}

// Limit coroutines
if (GetCoroutineCount() < MaxCoroutines)
{
    StartCoroutine(MyCoroutine);
}

// Clean up subscriptions
IEventSubscription subscription = SomeEvent.Subscribe(handler);
// Later: subscription.Unsubscribe();
```

#### 3. Event Not Firing
**Symptoms**: Event handlers never get called
**Causes**:
- Incorrect event name
- Component doesn't support the event
- Event subscription failed

**Solutions**:
```csharp
// Check if event exists
if (component.HasEvent("EventName"))
{
    component.Subscribe("EventName", handler);
}

// Use correct event names
rigidBody.Subscribe(CollisionEventType.Trigger, OnTrigger); // Correct
// NOT: rigidBody.Subscribe("Trigger", OnTrigger); // Wrong

// Verify subscription success
try
{
    component.Subscribe("EventName", handler);
}
catch (Exception e)
{
    Log.Write(LogLevel.Error, "Events", $"Subscription failed: {e.Message}");
}
```

#### 4. Coroutine Issues
**Symptoms**: Coroutines stop working or consume too much CPU
**Causes**:
- Infinite loops without Wait()
- Too many coroutines
- Coroutine exceptions

**Solutions**:
```csharp
// Always include Wait() in loops
private void MyCoroutine()
{
    while (condition)
    {
        DoWork();
        Wait(0.1); // REQUIRED - prevents infinite loop
    }
}

// Handle exceptions in coroutines
private void SafeCoroutine()
{
    try
    {
        while (isRunning)
        {
            DoWork();
            Wait(0.1);
        }
    }
    catch (Exception e)
    {
        Log.Write(LogLevel.Error, "Coroutine", $"Error: {e.Message}");
    }
}
```

#### 5. Performance Issues
**Symptoms**: Script responds slowly, high memory usage
**Causes**:
- Frequent string concatenation
- Inefficient searches
- Too many update loops

**Solutions**:
```csharp
// Use StringBuilder for multiple concatenations
var sb = new System.Text.StringBuilder();
sb.AppendLine("Line 1");
sb.AppendLine("Line 2");
string result = sb.ToString();

// Cache frequently accessed objects
private AgentPrivate[] cachedAgents;
public override void Init()
{
    cachedAgents = ScenePrivate.GetAgents().ToArray();
}

// Use appropriate update intervals
Wait(0.1); // 10 FPS - good for most updates
Wait(1.0); // 1 FPS - good for periodic checks
```

#### 6. Cross-Script Communication Problems
**Symptoms**: Scripts don't communicate properly
**Causes**:
- Incorrect event names
- Data type mismatches
- Event timing issues

**Solutions**:
```csharp
// Use consistent event naming
public const string MY_EVENT = "my_event"; // Define once, use everywhere

// Use proper data types
SimpleScriptEventData eventData = new SimpleScriptEventData("data");
PostScriptEvent(MY_EVENT, eventData);

// Handle timing with coroutines
SubscribeToScriptEvent("delayed_event", (data) => {
    StartCoroutine(() => {
        Wait(1.0); // Delay processing if needed
        ProcessEvent(data);
    });
});
```

### Debugging Tips

#### 1. Use Logging Effectively
```csharp
// Use different log levels
Log.Write(LogLevel.Info, "Script", "Normal operation");
Log.Write(LogLevel.Warning, "Script", "Something unusual");
Log.Write(LogLevel.Error, "Script", "Serious problem");

// Include context in logs
Log.Write($"Processing user {agent.AgentInfo.Name} at {DateTime.Now}");
```

#### 2. Add Debug Interactions
```csharp
[DefaultValue("Debug Info")]
public Interaction DebugInteraction;

private void OnDebug(InteractionData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
    if (agent != null)
    {
        string info = $"State: {currentState}, Count: {itemCount}, Memory: {Memory.Usage:F1}MB";
        agent.SendChat(info);
    }
}
```

#### 3. Use Error Mode Settings
```csharp
public override void Init()
{
    SetRelaxedErrorMode(); // For development - logs errors instead of crashing
    // SetStrictErrorMode(); // For production - crashes on errors
}
```

## Performance Guidelines

### Memory Management
- Monitor `Memory.Usage` regularly
- Clean up event subscriptions when done
- Limit the number of active coroutines
- Use object pooling for frequently created objects

### CPU Usage
- Always include `Wait()` calls in coroutine loops
- Use appropriate update intervals (not every frame)
- Cache expensive calculations
- Minimize string operations in hot paths

### Event Management
- Unsubscribe from events when no longer needed
- Use specific event types rather than broad subscriptions
- Avoid creating too many event subscriptions

### Best Practices Summary
1. **Always validate inputs** - Check for null agents, valid components
2. **Handle exceptions gracefully** - Use try-catch blocks around API calls
3. **Use appropriate logging** - Different levels for different severity
4. **Monitor resource usage** - Memory, coroutines, events
5. **Test edge cases** - Users leaving, network issues, component missing
6. **Document your code** - Clear variable names, comments for complex logic
7. **Follow naming conventions** - Consistent event names, clear method names