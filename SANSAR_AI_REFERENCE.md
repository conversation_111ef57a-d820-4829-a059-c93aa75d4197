# Sansar Scripting AI Reference Guide

## Overview

This document provides comprehensive reference information for AI agents generating Sansar scripts. Sansar uses a C#-based scripting system with two main namespaces: `Sansar.Script` (core framework) and `Sansar.Simulation` (world interaction).

## Core Architecture

### Script Base Classes

**SceneObjectScript**: For scripts attached to objects in the scene
```csharp
public class MyScript : SceneObjectScript
{
    public override void Init()
    {
        // Initialization code here
    }
}
```

**ScriptBase**: Abstract base providing core functionality
- **Lifecycle**: `Init()` called after interface initialization
- **Error Handling**: Three modes - Strict, Relaxed, Default
- **Coroutines**: Cooperative multitasking with `StartCoroutine()`
- **Events**: Cross-script communication via `PostScriptEvent()`
- **Logging**: Debug output via `Log` property

### Script Lifecycle Pattern

```csharp
public override void Init()
{
    // 1. Subscribe to events
    // 2. Get components
    // 3. Set up coroutines
    // 4. Initialize state
}
```

## Component System

### Core Components

**AnimationComponent**: Animation control
```csharp
AnimationComponent animComp;
if (ObjectPrivate.TryGetFirstComponent(out animComp))
{
    var anim = animComp.GetAnimation("AnimationName");
    anim.Play();
}
```

**RigidBodyComponent**: Physics and collision
```csharp
RigidBodyComponent rigidBody;
if (ObjectPrivate.TryGetFirstComponent(out rigidBody))
{
    rigidBody.Subscribe(CollisionEventType.Trigger, OnCollide);
}
```

**LightComponent**: Light control
```csharp
LightComponent light;
if (ObjectPrivate.TryGetFirstComponent(out light))
{
    light.SetColor(Sansar.Color.Red);
    light.SetIntensity(1.0f);
}
```

### Component Access Patterns

```csharp
// Get first component of type
ComponentType comp;
if (ObjectPrivate.TryGetFirstComponent(out comp)) { }

// Get all components of type
var components = ObjectPrivate.GetComponents<ComponentType>();

// Get component by index
var comp = ObjectPrivate.GetComponent(ComponentType.Animation, 0);
```

## Event System

### Event Subscription Pattern

```csharp
// Component events
MyInteraction.Subscribe(OnClick);
rigidBody.Subscribe(CollisionEventType.Trigger, OnCollide);

// Script events
SubscribeToScriptEvent("MyEvent", OnScriptEvent);

// Scene events
ScenePrivate.User.Subscribe(User.AddUser, OnUserJoin);
ScenePrivate.Chat.Subscribe(0, null, OnChat);
```

### Event Data Types

- **InteractionData**: User interactions with objects
- **CollisionData**: Physics collision information
- **UserData**: User join/leave events
- **ChatData**: Chat message events
- **ScriptEventData**: Custom script-to-script events

### Cross-Script Communication

```csharp
// Send event
PostScriptEvent("EventName", new SimpleScriptEventData("data"));

// Receive event
SubscribeToScriptEvent("EventName", (ScriptEventData data) => {
    // Handle event
});
```

## Coroutines

### Basic Coroutine Pattern

```csharp
public override void Init()
{
    StartCoroutine(MyCoroutine);
}

private void MyCoroutine()
{
    while (true)
    {
        // Do work
        Wait(1.0); // Wait 1 second
    }
}
```

### WaitFor Pattern (Async Operations)

```csharp
private void HandleInteraction()
{
    // Wait for an event to complete
    UserData userData = (UserData)WaitFor(ScenePrivate.User.Subscribe, User.AddUser, SessionId.Invalid);
    
    // Continue after event
    Log.Write("User joined: " + userData.User);
}
```

### Coroutine Management

```csharp
// Start coroutine
StartCoroutine(MethodName);
StartCoroutine(MethodName, parameter);

// Get current coroutine info
ICoroutine current = CurrentCoroutine;

// Get all coroutines
var allCoroutines = GetAllCoroutines();
```

## Agent/User Management

### Agent Access Patterns

```csharp
// Find agent by ID
AgentPrivate agent = ScenePrivate.FindAgent(agentId);

// Get agent from interaction
private void OnClick(InteractionData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
    agent.SendChat("Hello!");
}

// Check if agent is valid
if (agent != null && agent.IsValid)
{
    // Safe to use agent
}
```

### Scene Owner Checks

```csharp
private bool IsSceneOwner(AgentPrivate agent)
{
    return agent.AgentInfo.Handle.ToLower() == 
           ScenePrivate.SceneInfo.AvatarId.ToLower();
}
```

## Error Handling

### Error Modes

```csharp
public override void Init()
{
    SetRelaxedErrorMode(); // Log errors instead of throwing
    // SetStrictErrorMode(); // Throw exceptions
    // SetDefaultErrorMode(); // Balanced approach
}
```

### Exception Handling Pattern

```csharp
try
{
    // Sansar API calls
}
catch (Exception e)
{
    Log.Write(LogLevel.Warning, "ScriptName", $"Exception: {e.Message}");
}
```

## Editor Properties

### Common Attributes

```csharp
#region EditorProperties

[DefaultValue("Default Text")]
public string MyText;

[Range(0.0, 100.0)]
[DefaultValue(50.0)]
public double MyValue;

[Tooltip("This is a helpful tooltip")]
public bool MyFlag;

[DisplayName("Custom Name")]
public Interaction MyInteraction;

#endregion
```

### Property Types

- **string**: Text input
- **bool**: Checkbox
- **int/double**: Numeric input
- **Interaction**: Makes object clickable
- **SoundResource**: Audio file reference
- **MediaResource**: Media file reference

## Common Patterns

### Interaction Handler

```csharp
[DefaultValue("Click Me!")]
public Interaction MyInteraction;

public override void Init()
{
    MyInteraction.Subscribe(OnClick);
}

private void OnClick(InteractionData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
    if (agent != null && agent.IsValid)
    {
        agent.SendChat("Thanks for clicking!");
    }
}
```

### Chat Command Handler

```csharp
public override void Init()
{
    ScenePrivate.Chat.Subscribe(0, null, OnChat);
}

private void OnChat(ChatData data)
{
    var words = data.Message.Split(' ');
    if (words[0] == "/mycommand")
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.SourceId);
        // Handle command
    }
}
```

### Timer/Periodic Updates

```csharp
private void UpdateLoop()
{
    while (true)
    {
        // Update logic here
        Wait(0.1); // Update every 100ms
    }
}
```

### Audio Playback

```csharp
public SoundResource MySound;

private void PlaySoundForAgent(AgentPrivate agent)
{
    PlaySettings settings = PlaySettings.PlayOnce;
    settings.Loudness = 10.0f;
    PlayHandle handle = agent.PlaySound(MySound, settings);
}
```

## Data Persistence

### DataStore Usage

```csharp
DataStore.GetValue("key").Subscribe(result => {
    if (result.Success)
    {
        string value = result.Value;
        // Use stored value
    }
});

DataStore.SetValue("key", "value").Subscribe(result => {
    if (result.Success)
    {
        Log.Write("Data saved successfully");
    }
});
```

## Best Practices

### Performance

1. **Minimize event subscriptions** - Only subscribe to events you need
2. **Unsubscribe when done** - Clean up event subscriptions
3. **Use efficient coroutines** - Avoid busy waiting, use appropriate Wait() calls
4. **Cache component references** - Get components once in Init()

### Error Handling

1. **Always check validity** - Verify agents and components exist before use
2. **Use try-catch blocks** - Wrap Sansar API calls in exception handling
3. **Log errors appropriately** - Use appropriate LogLevel for debugging

### Code Organization

1. **Use regions** - Organize code with #region blocks
2. **Separate concerns** - Keep different functionality in separate methods
3. **Use meaningful names** - Clear variable and method names
4. **Comment complex logic** - Explain non-obvious code

### Security

1. **Validate user input** - Check chat commands and user data
2. **Check permissions** - Verify user rights before sensitive operations
3. **Never expose sensitive data** - Don't log passwords or tokens

## Keyboard Input System

### Command-Based Input
Sansar uses a command-based input system rather than direct key codes. This allows scripts to work across different input devices (keyboard, VR controllers, etc.).

### Available Commands and Default Key Bindings
```csharp
// Primary commands
Trigger         // Left Mouse Button
PrimaryAction   // F key
SecondaryAction // R key
Modifier        // Shift key

// Number actions
Action1         // 1 key
Action2         // 2 key
Action3         // 3 key
Action4         // 4 key
Action5         // 5 key
Action6         // 6 key
Action7         // 7 key
Action8         // 8 key
Action9         // 9 key
Action0         // 0 key

// Navigation
Confirm         // Enter key
Cancel          // Escape key
SelectLeft      // Left arrow
SelectRight     // Right arrow
SelectUp        // Up arrow
SelectDown      // Down arrow

// Keypad
Keypad0         // Numberpad 0
Keypad1         // Numberpad 1
Keypad2         // Numberpad 2
Keypad3         // Numberpad 3
Keypad4         // Numberpad 4
Keypad5         // Numberpad 5
Keypad6         // Numberpad 6
Keypad7         // Numberpad 7
Keypad8         // Numberpad 8
Keypad9         // Numberpad 9
KeypadEnter     // Numberpad Enter
```

### Command Actions
```csharp
CommandAction.Pressed   // Key pressed down
CommandAction.Released  // Key released
CommandAction.All       // Both pressed and released
```

### Basic Keyboard Input Pattern
```csharp
public override void Init()
{
    // Subscribe to user events to handle keyboard input
    ScenePrivate.User.Subscribe(User.AddUser, OnUserJoined);
}

private void OnUserJoined(UserData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.User);
    if (agent != null && agent.IsValid)
    {
        // Subscribe to specific commands for this user
        agent.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, OnPrimaryAction);
        agent.Client.SubscribeToCommand("Action1", CommandAction.Pressed, OnNumberKey);
        agent.Client.SubscribeToCommand("Confirm", CommandAction.Pressed, OnConfirm);
    }
}

private void OnPrimaryAction(CommandData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
    if (agent != null && agent.IsValid)
    {
        agent.SendChat("F key pressed!");
    }
}

private void OnNumberKey(CommandData data)
{
    AgentPrivate agent = ScenePrivate.FindAgent(data.AgentId);
    if (agent != null && agent.IsValid)
    {
        agent.SendChat($"Number key {data.Command} pressed!");
    }
}
```

### Multi-Key Handler Pattern
```csharp
public class KeyboardController : SceneObjectScript
{
    [DefaultValue(true)]
    public bool EnableKeyboardInput = true;

    private Dictionary<string, Action<CommandData>> keyHandlers;

    public override void Init()
    {
        if (EnableKeyboardInput)
        {
            SetupKeyHandlers();
            ScenePrivate.User.Subscribe(User.AddUser, OnUserJoined);
        }
    }

    private void SetupKeyHandlers()
    {
        keyHandlers = new Dictionary<string, Action<CommandData>>
        {
            { "PrimaryAction", OnPrimaryAction },
            { "SecondaryAction", OnSecondaryAction },
            { "Action1", OnAction1 },
            { "Action2", OnAction2 },
            { "Confirm", OnConfirm },
            { "Cancel", OnCancel }
        };
    }

    private void OnUserJoined(UserData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.User);
        if (agent != null && agent.IsValid)
        {
            foreach (var handler in keyHandlers)
            {
                agent.Client.SubscribeToCommand(handler.Key, CommandAction.Pressed, handler.Value);
            }
        }
    }

    private void OnPrimaryAction(CommandData data) { /* F key logic */ }
    private void OnSecondaryAction(CommandData data) { /* R key logic */ }
    private void OnAction1(CommandData data) { /* 1 key logic */ }
    private void OnAction2(CommandData data) { /* 2 key logic */ }
    private void OnConfirm(CommandData data) { /* Enter key logic */ }
    private void OnCancel(CommandData data) { /* Escape key logic */ }
}
```

### CommandData Properties
```csharp
private void OnKeyPressed(CommandData data)
{
    string command = data.Command;          // Command name (e.g., "PrimaryAction")
    SessionId agentId = data.AgentId;       // Which user pressed the key
    ControlPointType controlPoint = data.ControlPointType; // Input device type
    
    // Handle the command
    AgentPrivate agent = ScenePrivate.FindAgent(agentId);
    if (agent != null && agent.IsValid)
    {
        agent.SendChat($"Command {command} received");
    }
}
```

## Advanced Patterns

### ScriptLibrary Integration

```csharp
using ScriptLibrary;

public class MyScript : LibraryBase
{
    protected override void SimpleInit()
    {
        // Use LibraryBase functionality
        SubscribeToAll("EventName", OnEvent);
    }
}
```

### HTTP Client Usage

```csharp
public override void Init()
{
    StartCoroutine(MakeHttpRequest);
}

private void MakeHttpRequest()
{
    HttpClient client = ScenePrivate.CreateHttpClient();
    HttpRequestOptions options = new HttpRequestOptions();
    options.Method = HttpRequestMethod.GET;
    
    HttpRequestData request = WaitFor(client.Request, "https://api.example.com", options) as HttpRequestData;
    
    if (request.Success)
    {
        string response = request.Body;
        // Process response
    }
}
```

### Promise Pattern (Advanced)

```csharp
// From evoav's Promise library
using EvoAv.Promises;

private IPromise<string> FetchDataAsync()
{
    return new Promise<string>((resolve, reject) => {
        StartCoroutine(() => {
            try
            {
                // Async operation
                string result = "data";
                resolve(result);
            }
            catch (Exception e)
            {
                reject(e);
            }
        });
    });
}
```

## Common Issues and Solutions

### Null Reference Exceptions

```csharp
// Always check validity
AgentPrivate agent = ScenePrivate.FindAgent(agentId);
if (agent != null && agent.IsValid)
{
    // Safe to use agent
}

// Check component existence
if (ObjectPrivate.TryGetFirstComponent(out ComponentType comp))
{
    // Component exists
}
```

### Memory Management

```csharp
// Monitor memory usage
Log.Write($"Memory usage: {Memory.Usage}");

// Clean up event subscriptions
IEventSubscription subscription = SomeEvent.Subscribe(handler);
// Later: subscription.Unsubscribe();
```

### Coroutine Limits

```csharp
// Check coroutine count
if (GetCoroutineCount() < MaxCoroutines)
{
    StartCoroutine(NewCoroutine);
}
```

This reference guide provides the foundation for generating appropriate Sansar scripts. Always follow the established patterns and best practices for reliable, maintainable code.