This Script is used in my Synth, but, I am posting it because it shows how you can embed it in an object so that when you mouse over that object the interaction prompt comes up with a message.  In this way, you basically can add "hints" to an object.  

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/CustomCurrentInstrumentPlaying.png)

**Current Instrument Integration** - the text to display when mousing over (i.e. the hint).

![](https://github.com/mojoD/Sansar-Simple-And-Reflex-Script-Integration/blob/master/images/Hint.png)

In this script example, it is getting the hint to display via a reflective message.  So, it overrides the hint in the configuration when it gets this new hint via the reflective message.
